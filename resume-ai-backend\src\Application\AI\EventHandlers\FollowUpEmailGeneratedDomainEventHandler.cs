using Domain.AI;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.EventHandlers;

internal sealed class FollowUpEmailGeneratedDomainEventHandler : IDomainEventHandler<FollowUpEmailGeneratedDomainEvent>
{
    private readonly ILogger<FollowUpEmailGeneratedDomainEventHandler> _logger;

    public FollowUpEmailGeneratedDomainEventHandler(ILogger<FollowUpEmailGeneratedDomainEventHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(FollowUpEmailGeneratedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Follow-up email generated for JobApplication {JobApplicationId}. " +
            "Email Type: {EmailType}, Confidence: {Confidence}",
            domainEvent.JobApplicationId,
            domainEvent.EmailType,
            domainEvent.ConfidenceScore);

        // TODO: Add any additional logic needed when follow-up email is generated
        // For example: schedule email sending, update CRM, trigger reminders, etc.

        return Task.CompletedTask;
    }
}
