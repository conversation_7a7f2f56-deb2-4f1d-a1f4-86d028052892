using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

public interface IAIResumeCustomizationService
{
    Task<Result<AICustomizationResponse>> CustomizeResumeAsync(
        AICustomizationRequest request,
        CancellationToken cancellationToken = default);
}

public sealed record AICustomizationRequest(
    string JobTitle,
    string JobDescription,
    string CompanyUrl,
    string OriginalResumeContent,
    AIModelType? PreferredAIModel = null);

public sealed record AICustomizationResponse(
    string CustomizedResumeContent,
    string CustomizationSummary,
    string[] CustomizedKeyChanges,
    double ConfidenceScore);
