using Domain.AI;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.EventHandlers;

internal sealed class AgentWorkflowCompletedDomainEventHandler : IDomainEventHandler<AgentWorkflowCompletedDomainEvent>
{
    private readonly ILogger<AgentWorkflowCompletedDomainEventHandler> _logger;

    public AgentWorkflowCompletedDomainEventHandler(ILogger<AgentWorkflowCompletedDomainEventHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(AgentWorkflowCompletedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Multi-agent workflow completed for JobApplication {JobApplicationId}. " +
            "Success: {Success}, Execution Time: {ExecutionTime}ms, Completed Agents: {Agents}",
            domainEvent.JobApplicationId,
            domainEvent.IsSuccess,
            domainEvent.TotalExecutionTime.TotalMilliseconds,
            string.Join(", ", domainEvent.CompletedAgents));

        if (!domainEvent.IsSuccess && !string.IsNullOrEmpty(domainEvent.ErrorMessage))
        {
            _logger.LogWarning(
                "Multi-agent workflow had errors for JobApplication {JobApplicationId}: {ErrorMessage}",
                domainEvent.JobApplicationId,
                domainEvent.ErrorMessage);
        }

        // Log individual agent confidence scores
        foreach (var (agentType, confidence) in domainEvent.ConfidenceScores)
        {
            _logger.LogInformation(
                "Agent {AgentType} completed for JobApplication {JobApplicationId} with confidence: {Confidence}",
                agentType, domainEvent.JobApplicationId, confidence);
        }

        // TODO: Add any additional logic needed when agent workflow completes
        // For example: send notifications, update analytics, trigger follow-up actions, etc.

        return Task.CompletedTask;
    }
}
