using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Infrastructure.AI;

/// <summary>
/// Factory for creating AI agents
/// </summary>
internal sealed class AgentFactory : IAgentFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AgentFactory> _logger;

    public AgentFactory(IServiceProvider serviceProvider, ILogger<AgentFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public Result<IAgent<AgentRequest, AgentResponse>> CreateAgent(AgentType agentType)
    {
        _logger.LogDebug("Creating agent of type: {AgentType}", agentType);

        try
        {
            IAgent<AgentRequest, AgentResponse> agent = agentType switch
            {
                AgentType.ResumeCustomization => CreateResumeCustomizationAgentInternal(),
                AgentType.CompanyResearch => CreateCompanyResearchAgentInternal(),
                AgentType.CoverLetter => CreateCoverLetterAgentInternal(),
                AgentType.FollowUpEmail => CreateFollowUpEmailAgentInternal(),
                _ => throw new ArgumentException($"Unsupported agent type: {agentType}", nameof(agentType))
            };

            _logger.LogInformation("Successfully created agent of type: {AgentType}", agentType);
            return Result.Success(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create agent of type: {AgentType}", agentType);
            return Result.Failure<IAgent<AgentRequest, AgentResponse>>(
                AgentFactoryErrors.AgentCreationFailed(agentType, ex.Message));
        }
    }

    public Result<IResumeCustomizationAgent> CreateResumeCustomizationAgent()
    {
        _logger.LogDebug("Creating resume customization agent");

        try
        {
            var agent = _serviceProvider.GetRequiredService<IResumeCustomizationAgent>();
            _logger.LogInformation("Successfully created resume customization agent");
            return Result.Success(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create resume customization agent");
            return Result.Failure<IResumeCustomizationAgent>(
                AgentFactoryErrors.AgentCreationFailed(AgentType.ResumeCustomization, ex.Message));
        }
    }

    public Result<ICompanyResearchAgent> CreateCompanyResearchAgent()
    {
        _logger.LogDebug("Creating company research agent");

        try
        {
            var agent = _serviceProvider.GetRequiredService<ICompanyResearchAgent>();
            _logger.LogInformation("Successfully created company research agent");
            return Result.Success(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create company research agent");
            return Result.Failure<ICompanyResearchAgent>(
                AgentFactoryErrors.AgentCreationFailed(AgentType.CompanyResearch, ex.Message));
        }
    }

    public Result<ICoverLetterAgent> CreateCoverLetterAgent()
    {
        _logger.LogDebug("Creating cover letter agent");

        try
        {
            var agent = _serviceProvider.GetRequiredService<ICoverLetterAgent>();
            _logger.LogInformation("Successfully created cover letter agent");
            return Result.Success(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create cover letter agent");
            return Result.Failure<ICoverLetterAgent>(
                AgentFactoryErrors.AgentCreationFailed(AgentType.CoverLetter, ex.Message));
        }
    }

    public Result<IFollowUpEmailAgent> CreateFollowUpEmailAgent()
    {
        _logger.LogDebug("Creating follow-up email agent");

        try
        {
            var agent = _serviceProvider.GetRequiredService<IFollowUpEmailAgent>();
            _logger.LogInformation("Successfully created follow-up email agent");
            return Result.Success(agent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create follow-up email agent");
            return Result.Failure<IFollowUpEmailAgent>(
                AgentFactoryErrors.AgentCreationFailed(AgentType.FollowUpEmail, ex.Message));
        }
    }

    public bool IsAgentSupported(AgentType agentType)
    {
        return agentType switch
        {
            AgentType.ResumeCustomization => true,
            AgentType.CompanyResearch => true,
            AgentType.CoverLetter => true,
            AgentType.FollowUpEmail => true,
            _ => false
        };
    }

    private IAgent<AgentRequest, AgentResponse> CreateResumeCustomizationAgentInternal()
    {
        var agent = _serviceProvider.GetRequiredService<IResumeCustomizationAgent>();
        return new AgentWrapper<ResumeCustomizationRequest, ResumeCustomizationResponse>(agent);
    }

    private IAgent<AgentRequest, AgentResponse> CreateCompanyResearchAgentInternal()
    {
        var agent = _serviceProvider.GetRequiredService<ICompanyResearchAgent>();
        return new AgentWrapper<CompanyResearchRequest, CompanyResearchResponse>(agent);
    }

    private IAgent<AgentRequest, AgentResponse> CreateCoverLetterAgentInternal()
    {
        var agent = _serviceProvider.GetRequiredService<ICoverLetterAgent>();
        return new AgentWrapper<CoverLetterRequest, CoverLetterResponse>(agent);
    }

    private IAgent<AgentRequest, AgentResponse> CreateFollowUpEmailAgentInternal()
    {
        var agent = _serviceProvider.GetRequiredService<IFollowUpEmailAgent>();
        return new AgentWrapper<FollowUpEmailRequest, FollowUpEmailResponse>(agent);
    }
}

/// <summary>
/// Wrapper to adapt specific agent interfaces to the generic base interface
/// </summary>
internal sealed class AgentWrapper<TRequest, TResponse> : IAgent<AgentRequest, AgentResponse>
    where TRequest : AgentRequest
    where TResponse : AgentResponse
{
    private readonly IAgent<TRequest, TResponse> _innerAgent;

    public AgentWrapper(IAgent<TRequest, TResponse> innerAgent)
    {
        _innerAgent = innerAgent;
    }

    public AgentType AgentType => _innerAgent.AgentType;

    public async Task<Result<AgentResponse>> ProcessAsync(AgentRequest request, CancellationToken cancellationToken = default)
    {
        if (request is not TRequest typedRequest)
        {
            return Result.Failure<AgentResponse>(
                Error.Problem("AgentWrapper.InvalidRequestType", 
                    $"Request type {request.GetType().Name} is not compatible with agent type {AgentType}"));
        }

        var result = await _innerAgent.ProcessAsync(typedRequest, cancellationToken);
        
        return result.IsSuccess 
            ? Result.Success<AgentResponse>(result.Value) 
            : Result.Failure<AgentResponse>(result.Error);
    }
}
