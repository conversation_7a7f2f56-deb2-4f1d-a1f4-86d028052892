{"ConnectionStrings": {"Database": "Host=********;Port=5432;Database=resume-ai;Username=********;Password=********;Include Error Detail=true"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"ServerUrl": "http://seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Jwt": {"Secret": "super-duper-secret-value-that-should-be-in-user-secrets", "Issuer": "resume-ai", "Audience": "developers", "ExpirationInMinutes": 60}, "AIService": {"Endpoint": "https://generativelanguage.googleapis.com", "ApiKey": "AIzaSyDu_MdDecKVw-iBq_Fbowk0oJ7b59IyVx4", "Model": "gemini-2.5-pro", "TimeoutSeconds": 180, "MaxRetries": 1, "RetryDelaySeconds": 5, "MaxTokens": 4000, "Temperature": 0.3}, "AI": {"Models": {"DefaultModel": "Gemini", "Gemini": {"ApiKey": "AIzaSyCUKPpKX--rSEQGAnX-jf27og0qBX61Xds", "Model": "gemini-2.5-pro", "TimeoutSeconds": 240, "IsEnabled": true}, "OpenAI": {"ApiKey": "AIzaSyDu_MdDecKVw-iBq_Fbowk0oJ7b59IyVx4", "Model": "chatgpt-4o", "TimeoutSeconds": 240, "IsEnabled": false}}, "Gemini": {"ApiKey": "AIzaSyCUKPpKX--rSEQGAnX-jf27og0qBX61Xds", "Model": "gemini-2.5-pro", "TimeoutSeconds": 240}, "OpenAI": {"ApiKey": "AIzaSyDu_MdDecKVw-iBq_Fbowk0oJ7b59IyVx4", "Model": "chatgpt-4o", "TimeoutSeconds": 240}}}