# AI Follow-up Email Generation Prompts Configuration
# This file contains all prompts and instructions used for AI-powered follow-up email generation

prompts:
  follow_up_email:
    system_message: |
      You are an expert career coach and professional communication specialist with 
      extensive experience in job search strategies. Your task is to generate effective 
      follow-up emails that help job applicants maintain professional relationships 
      and demonstrate continued interest while respecting professional boundaries. 
      You understand timing, tone, and what makes follow-up emails successful.

    user_prompt_template: |
      Please generate a professional follow-up email for the following job application.

      **Job Details:**
      - Position: {job_title}
      - Company: {company_url}
      - Application Date: {application_date}
      - Email Type: {email_type}

      **Company Research (if available):**
      {company_research}

      **Instructions:**
      {instructions}

      **Response Format:**
      {response_format}

    instructions: |
      1. Create a professional, concise follow-up email appropriate for the email type
      2. Use a respectful and professional tone throughout
      3. **Keep the email between 500 and 1500 words to ensure it is detailed but not overwhelming.**
      4. Show continued interest without being pushy or demanding
      5. Include relevant company information if available to show genuine interest
      6. Provide value or additional information when appropriate
      7. Use a clear, professional subject line
      8. End with a polite call-to-action or next step
      9. Maintain professional boundaries and respect the hiring process
      10. Adapt the tone and content based on the email type and timing

    response_format: |
      IMPORTANT: Respond with ONLY a valid JSON object. Do NOT wrap your response in markdown code blocks or any other formatting.

      Return a JSON object with these exact properties:
      - "emailSubject": A professional subject line for the email
      - "emailContent": The complete email content in professional format with HTML only
      - "summary": A brief summary of the email approach and purpose (max 150 characters)
      - "emailType": The type of follow-up email (matches input)
      - "confidence": A confidence score (0.0-1.0) for the email quality

      Your response must start with { and end with } - no markdown, no code blocks, no additional text.

      Example format:
        {
          "emailSubject": "Following up on Software Engineer Application",
          "emailContent": "<p>Dear Hiring Manager,</p>\n\n<p>I hope this email finds you well. I wanted to follow up on my application for the Software Engineer position...</p>",
          "summary": "Professional follow-up expressing continued interest and availability for next steps",
          "emailType": "Initial",
          "confidence": 0.85
        }

    fallback_instructions: |
      If you cannot generate a proper follow-up email:
      1. Create a basic professional template
      2. Set confidence to 0.4 or lower
      3. Provide explanation in the summary
      4. Use generic but appropriate language

# Configuration for different email types
email_types:
  initial:
    timing: "1-2 weeks after application"
    additional_instructions: |
      - Express continued interest in the position
      - Briefly reiterate key qualifications
      - Ask about the timeline for the hiring process
      - Keep it short and professional
      - Show enthusiasm without being pushy

  post_interview:
    timing: "24-48 hours after interview"
    additional_instructions: |
      - Thank the interviewer(s) for their time
      - Reiterate interest in the position
      - Address any concerns or questions that came up
      - Provide additional information if relevant
      - Mention specific discussion points from the interview

  thank_you:
    timing: "Same day or next day after interview"
    additional_instructions: |
      - Express genuine gratitude for the opportunity
      - Mention specific aspects of the conversation you enjoyed
      - Reiterate your interest and qualifications
      - Keep it warm but professional
      - Include any promised follow-up materials

  status_inquiry:
    timing: "2-3 weeks after last contact"
    additional_instructions: |
      - Politely inquire about the status of your application
      - Reaffirm your interest in the position
      - Mention any updates to your availability or qualifications
      - Be patient and understanding of their process
      - Provide a gentle timeline for your decision-making

# Industry-specific considerations
industries:
  technology:
    tone: "Professional but slightly casual, show technical enthusiasm"
    focus: "Technical fit, innovation, problem-solving"
    
  finance:
    tone: "Formal and conservative, emphasize reliability"
    focus: "Analytical skills, attention to detail, compliance"
    
  healthcare:
    tone: "Professional and caring, emphasize patient focus"
    focus: "Patient care, regulatory knowledge, teamwork"
    
  creative:
    tone: "Professional but creative, show personality"
    focus: "Creative vision, collaboration, portfolio work"

# Quality control parameters
quality_control:
  min_confidence_threshold: 0.5
  max_content_length: 2000   # Maximum characters in email content
  min_content_length: 500   # Minimum characters for complete email
  max_subject_length: 60    # Maximum characters in subject line
  required_elements:
    - "professional greeting"
    - "purpose statement"
    - "main content"
    - "professional closing"
  
  validation_rules:
    - "Must maintain professional tone"
    - "Should be concise and focused"
    - "Must respect professional boundaries"
    - "Should provide clear next steps or call-to-action"

# Error handling messages
error_messages:
  invalid_email_type: "Email type is not recognized or supported"
  insufficient_context: "Not enough context provided to create effective follow-up email"
  processing_timeout: "Email generation took too long, returning basic template"
  api_error: "Follow-up email service temporarily unavailable, please try again later"
