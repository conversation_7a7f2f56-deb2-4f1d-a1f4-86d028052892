namespace Application.Abstractions.BackgroundJobs;

public interface IBackgroundJobService
{
    /// <summary>
    /// Enqueues an AI resume customization job to be processed in the background
    /// </summary>
    /// <param name="jobId">The ID of the job to process</param>
    /// <returns>The background job ID</returns>
    string EnqueueAIResumeCustomization(Guid jobId);

    /// <summary>
    /// Enqueues a multi-agent AI workflow job to be processed in the background
    /// </summary>
    /// <param name="jobId">The ID of the job to process</param>
    /// <returns>The background job ID</returns>
    string EnqueueMultiAgentWorkflow(Guid jobId);
}
