using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Agent responsible for generating follow-up email templates
/// </summary>
public interface IFollowUpEmailAgent : IAgent<FollowUpEmailRequest, FollowUpEmailResponse>
{
}

/// <summary>
/// Request for follow-up email generation
/// </summary>
public sealed record FollowUpEmailRequest(
    Guid JobApplicationId,
    string JobTitle,
    string CompanyUrl,
    DateTime ApplicationDate,
    FollowUpEmailType EmailType = FollowUpEmailType.Initial,
    CompanyResearchResponse? CompanyResearch = null,
    AIModelType? PreferredAIModel = null) : AgentRequest(JobApplicationId, PreferredAIModel);

/// <summary>
/// Response from follow-up email generation
/// </summary>
public sealed record FollowUpEmailResponse(
    string EmailSubject,
    string EmailContent,
    string Summary,
    FollowUpEmailType EmailType,
    double ConfidenceScore,
    DateTime ProcessedAt,
    AIModelType AIModelUsed) : AgentResponse(ConfidenceScore, ProcessedAt, AIModelUsed);

/// <summary>
/// Types of follow-up emails
/// </summary>
public enum FollowUpEmailType
{
    /// <summary>
    /// Initial follow-up after application submission
    /// </summary>
    Initial = 1,
    
    /// <summary>
    /// Follow-up after interview
    /// </summary>
    PostInterview = 2,
    
    /// <summary>
    /// Thank you email after interview
    /// </summary>
    ThankYou = 3,
    
    /// <summary>
    /// Status inquiry email
    /// </summary>
    StatusInquiry = 4
}
