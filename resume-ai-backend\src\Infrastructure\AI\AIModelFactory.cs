using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using SharedKernel;

namespace Infrastructure.AI;

/// <summary>
/// Factory implementation for creating AI model instances
/// </summary>
internal sealed class AIModelFactory : IAIChatServiceFactory
{
    private readonly AIModelsOptions _options;
    private readonly ILogger<AIModelFactory> _logger;

    public AIModelFactory(IOptions<AIModelsOptions> options, ILogger<AIModelFactory> logger)
    {
        _options = options.Value;
        _logger = logger;
    }

    public Result<IChatCompletionService> CreateChatCompletionService(AIModelType? modelType = null)
    {
        AIModelType targetModel = modelType ?? _options.DefaultModel;
        
        _logger.LogDebug("Creating chat completion service for AI model: {ModelType}", targetModel);

        if (!IsModelSupported(targetModel))
        {
            _logger.LogError("AI model {ModelType} is not supported or not properly configured", targetModel);
            return Result.Failure<IChatCompletionService>(AIModelFactoryErrors.ModelNotSupported(targetModel));
        }

        try
        {
            IChatCompletionService service = targetModel switch
            {
                AIModelType.Gemini => CreateGeminiService(),
                AIModelType.OpenAI => throw new NotSupportedException("OpenAI connector is not currently installed"),
                _ => throw new ArgumentException($"Unsupported AI model type: {targetModel}", nameof(modelType))
            };

            _logger.LogInformation("Successfully created chat completion service for AI model: {ModelType}", targetModel);
            return Result.Success(service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create chat completion service for AI model: {ModelType}", targetModel);
            return Result.Failure<IChatCompletionService>(
                AIModelFactoryErrors.ModelCreationFailed(targetModel, ex.Message));
        }
    }

    public AIModelType GetDefaultModelType()
    {
        return _options.DefaultModel;
    }

    public bool IsModelSupported(AIModelType modelType)
    {
        try
        {
            AIModelConfiguration config = _options.GetModelConfiguration(modelType);
            return config.IsEnabled && 
                   !string.IsNullOrWhiteSpace(config.ApiKey) && 
                   !string.IsNullOrWhiteSpace(config.Model);
        }
        catch (ArgumentException)
        {
            return false;
        }
    }

    public IEnumerable<AIModelType> GetSupportedModels()
    {
        var supportedModels = new List<AIModelType>();

        foreach (AIModelType modelType in Enum.GetValues<AIModelType>())
        {
            if (IsModelSupported(modelType))
            {
                supportedModels.Add(modelType);
            }
        }

        return supportedModels;
    }

    private IChatCompletionService CreateGeminiService()
    {
        AIModelConfiguration config = _options.Gemini;
        
        if (string.IsNullOrWhiteSpace(config.ApiKey))
        {
            throw new InvalidOperationException("Google Gemini API key is not configured");
        }

        if (string.IsNullOrWhiteSpace(config.Model))
        {
            throw new InvalidOperationException("Google Gemini model is not configured");
        }

        _logger.LogDebug("Creating Google Gemini service with model: {Model}", config.Model);
        
        return new GoogleAIGeminiChatCompletionService(
            modelId: config.Model,
            apiKey: config.ApiKey);
    }

    // OpenAI service creation is not implemented as the connector is not installed
    // To add OpenAI support, install Microsoft.SemanticKernel.Connectors.OpenAI package
    // and implement this method
}
