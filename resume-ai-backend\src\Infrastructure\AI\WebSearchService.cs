#pragma warning disable SKEXP0050 // Type is for evaluation purposes only and is subject to change or removal in future updates
#pragma warning disable SKEXP0001 // Type is for evaluation purposes only and is subject to change or removal in future updates

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.Data;
using Microsoft.SemanticKernel.Plugins.Web.Google;
using SharedKernel;
using System.Text.RegularExpressions;

namespace Infrastructure.AI;

/// <summary>
/// Implementation of web search service using Google Custom Search
/// </summary>
internal sealed class WebSearchService : IWebSearchService, IDisposable
{
    private readonly GoogleTextSearch _googleTextSearch;
    private readonly ILogger<WebSearchService> _logger;
    private readonly WebSearchOptions _options;

    public WebSearchService(
        IOptions<WebSearchOptions> options,
        ILogger<WebSearchService> logger)
    {
        _options = options.Value;
        _logger = logger;

        // Initialize Google Text Search
        _googleTextSearch = new GoogleTextSearch(
            searchEngineId: _options.GoogleSearchEngineId,
            apiKey: _options.GoogleApiKey);
    }

    public async Task<Result<CompanySearchResults>> SearchCompanyInformationAsync(
        string companyUrl, 
        string jobTitle, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting web search for company: {CompanyUrl}", companyUrl);

            // Extract company name from URL for better search queries
            var companyName = ExtractCompanyNameFromUrl(companyUrl);
            
            // Create search queries
            var searchQueries = CreateSearchQueries(companyName, companyUrl, jobTitle);
            
            var allResults = new List<SearchResult>();
            
            // Perform searches for each query
            foreach (var query in searchQueries)
            {
                _logger.LogDebug("Executing search query: {Query}", query);
                
                var searchOptions = new TextSearchOptions
                {
                    Top = 3, // Limit results per query
                    Filter = new TextSearchFilter().Equality("siteSearch", GetDomainFromUrl(companyUrl))
                };

                try
                {
                    var searchResults = await _googleTextSearch.GetTextSearchResultsAsync(
                        query, 
                        searchOptions, 
                        cancellationToken);

                    await foreach (var result in searchResults.Results)
                    {
                        allResults.Add(new SearchResult(
                            Name: result.Name ?? "Unknown Source",
                            Value: result.Value ?? "No content available",
                            Link: result.Link ?? companyUrl));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Search query failed: {Query}", query);
                    // Continue with other queries
                }
            }

            // If site-specific search didn't yield results, try broader search
            if (allResults.Count == 0)
            {
                _logger.LogInformation("Site-specific search yielded no results, trying broader search");
                allResults.AddRange(await PerformBroaderSearch(companyName, jobTitle, cancellationToken));
            }

            var confidenceScore = CalculateConfidenceScore(allResults, companyName);
            
            _logger.LogInformation("Web search completed. Found {ResultCount} results with confidence {Confidence}", 
                allResults.Count, confidenceScore);

            return Result.Success(new CompanySearchResults(
                CompanyName: companyName,
                SearchResults: allResults.AsReadOnly(),
                ConfidenceScore: confidenceScore));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during web search for company: {CompanyUrl}", companyUrl);
            return Result.Failure<CompanySearchResults>(
                Error.Problem("WebSearchService.SearchFailed", $"Web search failed: {ex.Message}"));
        }
    }

    private static string ExtractCompanyNameFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            var domain = uri.Host.ToLowerInvariant();
            
            // Remove common prefixes
            domain = domain.Replace("www.", "");
            
            // Extract the main part before the TLD
            var parts = domain.Split('.');
            if (parts.Length >= 2)
            {
                return parts[0].Replace("-", " ").Replace("_", " ");
            }
            
            return domain;
        }
        catch
        {
            return "Unknown Company";
        }
    }

    private static string GetDomainFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return string.Empty;
        }
    }

    private static List<string> CreateSearchQueries(string companyName, string companyUrl, string jobTitle)
    {
        var queries = new List<string>
        {
            $"{companyName} company information about us",
            $"{companyName} company mission values culture",
            $"{companyName} company size industry business",
            $"{companyName} recent news achievements",
            $"{companyName} products services solutions"
        };

        // Add job-specific query if job title is provided
        if (!string.IsNullOrWhiteSpace(jobTitle))
        {
            queries.Add($"{companyName} {jobTitle} job career opportunities");
        }

        return queries;
    }

    private async Task<List<SearchResult>> PerformBroaderSearch(
        string companyName, 
        string jobTitle, 
        CancellationToken cancellationToken)
    {
        var results = new List<SearchResult>();
        
        try
        {
            var broadQuery = $"{companyName} company information";
            var searchOptions = new TextSearchOptions { Top = 5 };

            var searchResults = await _googleTextSearch.GetTextSearchResultsAsync(
                broadQuery, 
                searchOptions, 
                cancellationToken);

            await foreach (var result in searchResults.Results)
            {
                results.Add(new SearchResult(
                    Name: result.Name ?? "Unknown Source",
                    Value: result.Value ?? "No content available",
                    Link: result.Link ?? string.Empty));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Broader search also failed for company: {CompanyName}", companyName);
        }

        return results;
    }

    private static double CalculateConfidenceScore(List<SearchResult> results, string companyName)
    {
        if (results.Count == 0)
            return 0.1;

        var score = 0.3; // Base score
        
        // Increase score based on number of results
        score += Math.Min(results.Count * 0.1, 0.4);
        
        // Increase score if company name appears in results
        var companyMentions = results.Count(r => 
            r.Value.Contains(companyName, StringComparison.OrdinalIgnoreCase) ||
            r.Name.Contains(companyName, StringComparison.OrdinalIgnoreCase));
        
        score += Math.Min(companyMentions * 0.05, 0.3);
        
        return Math.Min(score, 1.0);
    }

    public void Dispose()
    {
        _googleTextSearch?.Dispose();
    }
}

/// <summary>
/// Configuration options for web search
/// </summary>
public sealed class WebSearchOptions
{
    public const string SectionName = "WebSearch";
    
    /// <summary>
    /// Google Custom Search Engine ID
    /// </summary>
    public string GoogleSearchEngineId { get; set; } = string.Empty;
    
    /// <summary>
    /// Google API Key for Custom Search
    /// </summary>
    public string GoogleApiKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Maximum number of search results to process
    /// </summary>
    public int MaxResults { get; set; } = 10;
    
    /// <summary>
    /// Timeout for search operations in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}
