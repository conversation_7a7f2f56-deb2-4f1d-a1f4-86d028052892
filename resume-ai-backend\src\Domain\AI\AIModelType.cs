namespace Domain.AI;

/// <summary>
/// Represents the available AI models for resume customization
/// </summary>
public enum AIModelType
{
    /// <summary>
    /// Google Gemini AI model (default)
    /// </summary>
    Gemini = 1,
    
    /// <summary>
    /// OpenAI GPT model
    /// </summary>
    OpenAI = 2
}

/// <summary>
/// Represents the different types of AI agents in the multi-agent system
/// </summary>
public enum AgentType
{
    /// <summary>
    /// Agent responsible for customizing resumes based on job requirements
    /// </summary>
    ResumeCustomization = 1,

    /// <summary>
    /// Agent responsible for researching company information from URLs
    /// </summary>
    CompanyResearch = 2,

    /// <summary>
    /// Agent responsible for generating tailored cover letters
    /// </summary>
    CoverLetter = 3,

    /// <summary>
    /// Agent responsible for generating follow-up email templates
    /// </summary>
    FollowUpEmail = 4
}
