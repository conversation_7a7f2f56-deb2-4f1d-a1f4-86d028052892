using SharedKernel;

namespace Domain.AI;

/// <summary>
/// Domain event raised when an agent workflow is started
/// </summary>
public sealed record AgentWorkflowStartedDomainEvent(
    Guid JobApplicationId,
    AgentType[] RequestedAgents,
    AIModelType? PreferredAIModel = null) : IDomainEvent;

/// <summary>
/// Domain event raised when an agent completes processing
/// </summary>
public sealed record AgentProcessingCompletedDomainEvent(
    Guid JobApplicationId,
    AgentType AgentType,
    double ConfidenceScore,
    bool IsSuccess,
    string? ErrorMessage = null) : IDomainEvent;

/// <summary>
/// Domain event raised when an agent workflow is completed
/// </summary>
public sealed record AgentWorkflowCompletedDomainEvent(
    Guid JobApplicationId,
    AgentType[] CompletedAgents,
    Dictionary<AgentType, double> ConfidenceScores,
    TimeSpan TotalExecutionTime,
    bool IsSuccess,
    string? ErrorMessage = null) : IDomainEvent;

/// <summary>
/// Domain event raised when company research is completed
/// </summary>
public sealed record CompanyResearchCompletedDomainEvent(
    Guid JobApplicationId,
    string CompanyName,
    string CompanyUrl,
    double ConfidenceScore) : IDomainEvent;

/// <summary>
/// Domain event raised when cover letter generation is completed
/// </summary>
public sealed record CoverLetterGeneratedDomainEvent(
    Guid JobApplicationId,
    string JobTitle,
    double ConfidenceScore) : IDomainEvent;

/// <summary>
/// Domain event raised when follow-up email generation is completed
/// </summary>
public sealed record FollowUpEmailGeneratedDomainEvent(
    Guid JobApplicationId,
    string EmailType,
    double ConfidenceScore) : IDomainEvent;
