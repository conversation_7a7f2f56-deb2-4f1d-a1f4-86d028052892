using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.SemanticKernel.ChatCompletion;
using SharedKernel;

namespace Infrastructure.AI;

/// <summary>
/// Infrastructure-specific factory interface for creating chat completion services
/// </summary>
internal interface IAIChatServiceFactory : IAIModelFactory
{
    /// <summary>
    /// Creates a chat completion service for the specified AI model type
    /// </summary>
    /// <param name="modelType">The AI model type to create. If null, uses the default model.</param>
    /// <returns>A result containing the chat completion service or an error</returns>
    Result<IChatCompletionService> CreateChatCompletionService(AIModelType? modelType = null);
}
