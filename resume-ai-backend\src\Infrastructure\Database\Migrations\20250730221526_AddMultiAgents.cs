﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddMultiAgents : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "company_research_confidence",
                schema: "public",
                table: "job_applications",
                type: "double precision",
                precision: 3,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "company_research_data",
                schema: "public",
                table: "job_applications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "company_research_processed_at",
                schema: "public",
                table: "job_applications",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "cover_letter_confidence",
                schema: "public",
                table: "job_applications",
                type: "double precision",
                precision: 3,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "cover_letter_content",
                schema: "public",
                table: "job_applications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "cover_letter_key_highlights",
                schema: "public",
                table: "job_applications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "cover_letter_processed_at",
                schema: "public",
                table: "job_applications",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "cover_letter_summary",
                schema: "public",
                table: "job_applications",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "follow_up_email_confidence",
                schema: "public",
                table: "job_applications",
                type: "double precision",
                precision: 3,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "follow_up_email_content",
                schema: "public",
                table: "job_applications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "follow_up_email_processed_at",
                schema: "public",
                table: "job_applications",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "follow_up_email_subject",
                schema: "public",
                table: "job_applications",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "follow_up_email_summary",
                schema: "public",
                table: "job_applications",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "follow_up_email_type",
                schema: "public",
                table: "job_applications",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "company_research_confidence",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "company_research_data",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "company_research_processed_at",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "cover_letter_confidence",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "cover_letter_content",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "cover_letter_key_highlights",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "cover_letter_processed_at",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "cover_letter_summary",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_confidence",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_content",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_processed_at",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_subject",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_summary",
                schema: "public",
                table: "job_applications");

            migrationBuilder.DropColumn(
                name: "follow_up_email_type",
                schema: "public",
                table: "job_applications");
        }
    }
}
