﻿using Domain.AI;

namespace Domain.Jobs;
public class Job : BaseEntity
{
    public Guid UserId { get; set; }
    public string JobTitle { get; set; }
    public string JobDescription { get; set; }
    public string JobPostingUrl { get; set; }
    public string CompanyUrl { get; set; }
    public DateTime? AppliedAt { get; set; }

    // AI Processing Status
    public JobStatus Status { get; set; } = JobStatus.Created;

    // Background Job ID for tracking multi-agent workflow
    public string? BackgroundJobId { get; set; }

    // AI Model Preference
    public AIModelType? PreferredAIModel { get; set; }

    public void UpdateStatus(JobStatus newStatus)
    {
        if (Status != newStatus)
        {
            JobStatus oldStatus = Status;
            Status = newStatus;

            Raise(new JobStatusChangedDomainEvent(Id, oldStatus, newStatus));
        }
    }

    public void UpdateBackgroundJobId(string backgroundJobId)
    {
        BackgroundJobId = backgroundJobId;
    }
}
