using Domain.AI;
using Shared<PERSON>ernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Factory for creating AI agents
/// </summary>
public interface IAgentFactory
{
    /// <summary>
    /// Create an agent of the specified type
    /// </summary>
    /// <param name="agentType">The type of agent to create</param>
    /// <returns>The created agent</returns>
    Result<IAgent<AgentRequest, AgentResponse>> CreateAgent(AgentType agentType);
    
    /// <summary>
    /// Create a resume customization agent
    /// </summary>
    /// <returns>The resume customization agent</returns>
    Result<IResumeCustomizationAgent> CreateResumeCustomizationAgent();
    
    /// <summary>
    /// Create a company research agent
    /// </summary>
    /// <returns>The company research agent</returns>
    Result<ICompanyResearchAgent> CreateCompanyResearchAgent();
    
    /// <summary>
    /// Create a cover letter agent
    /// </summary>
    /// <returns>The cover letter agent</returns>
    Result<ICoverLetterAgent> CreateCoverLetterAgent();
    
    /// <summary>
    /// Create a follow-up email agent
    /// </summary>
    /// <returns>The follow-up email agent</returns>
    Result<IFollowUpEmailAgent> CreateFollowUpEmailAgent();
    
    /// <summary>
    /// Check if an agent type is supported
    /// </summary>
    /// <param name="agentType">The agent type to check</param>
    /// <returns>True if supported, false otherwise</returns>
    bool IsAgentSupported(AgentType agentType);
}

/// <summary>
/// Errors related to agent factory operations
/// </summary>
public static class AgentFactoryErrors
{
    public static Error AgentNotSupported(AgentType agentType) => Error.Problem(
        "AgentFactory.AgentNotSupported",
        $"Agent type '{agentType}' is not supported or not properly configured");
    
    public static Error AgentCreationFailed(AgentType agentType, string reason) => Error.Problem(
        "AgentFactory.AgentCreationFailed",
        $"Failed to create agent of type '{agentType}': {reason}");
}
