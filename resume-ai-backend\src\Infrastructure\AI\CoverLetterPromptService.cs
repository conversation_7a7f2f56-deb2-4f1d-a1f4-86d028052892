using Microsoft.Extensions.Logging;

namespace Infrastructure.AI;

public interface ICoverLetterPromptService : IBasePromptService<CoverLetterPromptConfiguration>
{
    string GetUserPrompt(string jobTitle, string jobDescription, string companyUrl, string resumeContent);
    string GetInstructions(string? jobType = null);
}

internal sealed class CoverLetterPromptService : BasePromptService<CoverLetterPromptConfiguration>, ICoverLetterPromptService
{
    public CoverLetterPromptService(ILogger<CoverLetterPromptService> logger) : base(logger)
    {
    }

    protected override string ResourceName => "Infrastructure.AI.Prompts.cover-letter-prompts.yaml";

    public string GetSystemMessage()
    {
        return Configuration.Prompts.CoverLetter.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string jobDescription, string companyUrl, string resumeContent)
    {
        var template = Configuration.Prompts.CoverLetter.UserPromptTemplate;
        var instructions = GetInstructions();
        var responseFormat = GetResponseFormat();

        var replacements = new Dictionary<string, string>
        {
            ["job_title"] = jobTitle,
            ["job_description"] = jobDescription,
            ["company_url"] = companyUrl,
            ["resume_content"] = resumeContent,
            ["company_research"] = "",
            ["instructions"] = instructions,
            ["response_format"] = responseFormat
        };

        return ReplaceTemplatePlaceholders(template, replacements);
    }

    public string GetInstructions(string? jobType = null)
    {
        var baseInstructions = Configuration.Prompts.CoverLetter.Instructions;

        return GetCategoryInstructions(
            baseInstructions,
            jobType,
            Configuration.JobTypes,
            config => config.AdditionalInstructions);
    }

    public string GetResponseFormat()
    {
        return Configuration.Prompts.CoverLetter.ResponseFormat;
    }

    public CoverLetterPromptConfiguration GetConfiguration()
    {
        return Configuration;
    }

    protected override CoverLetterPromptConfiguration CreateDefaultConfiguration()
    {
        return new CoverLetterPromptConfiguration
        {
            Prompts = new CoverLetterPromptsSection
            {
                CoverLetter = new CoverLetterPrompt
                {
                    SystemMessage = "You are an expert career counselor and professional writer. Generate compelling cover letters.",
                    UserPromptTemplate = "Generate a cover letter for {job_title} at {company_url}. Job: {job_description}. Resume: {resume_content}. {instructions} {response_format}",
                    Instructions = "1. Create professional cover letter\n2. Highlight relevant experience\n3. Show genuine interest",
                    ResponseFormat = "Respond with JSON containing coverLetterContent, summary, keyHighlights, and confidence."
                }
            },
            JobTypes = new Dictionary<string, JobTypeConfig>(),
            QualityControl = new CoverLetterQualityControlConfig
            {
                MinConfidenceThreshold = 0.5,
                MaxContentLength = 20000,
                MinContentLength = 500,
                RequiredElements = new[] { "professional greeting", "opening statement", "body paragraphs", "closing statement" },
                ValidationRules = new[] { "Professional tone", "Specific examples", "Tailored content" }
            }
        };
    }
}

// Configuration classes
public sealed class CoverLetterPromptConfiguration
{
    public CoverLetterPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, JobTypeConfig> JobTypes { get; set; } = new();
    public CoverLetterQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class CoverLetterPromptsSection
{
    public CoverLetterPrompt CoverLetter { get; set; } = new();
}

public sealed class CoverLetterPrompt : BasePromptConfig
{
}

public sealed class JobTypeConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class CoverLetterQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public int MinContentLength { get; set; }
    public string[] RequiredElements { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
