using Domain.AI;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.EventHandlers;

internal sealed class CompanyResearchCompletedDomainEventHandler : IDomainEventHandler<CompanyResearchCompletedDomainEvent>
{
    private readonly ILogger<CompanyResearchCompletedDomainEventHandler> _logger;

    public CompanyResearchCompletedDomainEventHandler(ILogger<CompanyResearchCompletedDomainEventHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(CompanyResearchCompletedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Company research completed for JobApplication {JobApplicationId}. " +
            "Company: {CompanyName}, URL: {CompanyUrl}, Confidence: {Confidence}",
            domainEvent.JobApplicationId,
            domainEvent.CompanyName,
            domainEvent.CompanyUrl,
            domainEvent.ConfidenceScore);

        // TODO: Add any additional logic needed when company research completes
        // For example: cache company data, update company database, trigger related workflows, etc.

        return Task.CompletedTask;
    }
}
