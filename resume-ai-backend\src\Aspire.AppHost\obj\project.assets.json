{"version": 3, "targets": {"net9.0": {"Aspire.Dashboard.Sdk.win-x64/9.3.1": {"type": "package", "build": {"buildTransitive/Aspire.Dashboard.Sdk.win-x64.props": {}, "buildTransitive/Aspire.Dashboard.Sdk.win-x64.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Aspire.Dashboard.Sdk.win-x64.props": {}, "buildMultiTargeting/Aspire.Dashboard.Sdk.win-x64.targets": {}}}, "Aspire.Hosting/9.3.1": {"type": "package", "dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.15", "Microsoft.Extensions.Hosting": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "compile": {"lib/net8.0/Aspire.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Aspire.Hosting.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Aspire.Hosting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Aspire.Hosting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Aspire.Hosting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Aspire.Hosting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Aspire.Hosting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Aspire.Hosting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Aspire.Hosting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Aspire.Hosting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Aspire.Hosting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Aspire.Hosting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Aspire.Hosting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Aspire.Hosting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Aspire.Hosting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Aspire.Hosting.AppHost/9.3.1": {"type": "package", "dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.1", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "compile": {"lib/net9.0/Aspire.Hosting.AppHost.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Aspire.Hosting.AppHost.dll": {"related": ".xml"}}, "build": {"build/Aspire.Hosting.AppHost.props": {}, "build/Aspire.Hosting.AppHost.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Aspire.Hosting.AppHost.props": {}, "buildMultiTargeting/Aspire.Hosting.AppHost.targets": {}}}, "Aspire.Hosting.Orchestration.win-x64/9.3.1": {"type": "package", "build": {"buildTransitive/Aspire.Hosting.Orchestration.win-x64.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Aspire.Hosting.Orchestration.win-x64.targets": {}}}, "Aspire.Hosting.PostgreSQL/9.3.1": {"type": "package", "dependencies": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.1", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.15", "Microsoft.Extensions.Hosting": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "compile": {"lib/net8.0/Aspire.Hosting.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Aspire.Hosting.PostgreSQL.dll": {"related": ".xml"}}}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.11", "Npgsql": "8.0.3"}, "compile": {"lib/net8.0/HealthChecks.NpgSql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/HealthChecks.NpgSql.dll": {"related": ".xml"}}}, "AspNetCore.HealthChecks.Uris/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.11", "Microsoft.Extensions.Http": "8.0.0"}, "compile": {"lib/net8.0/HealthChecks.Uris.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/HealthChecks.Uris.dll": {"related": ".xml"}}}, "Fractions/7.3.0": {"type": "package", "compile": {"lib/netstandard2.1/Fractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Fractions.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.2": {"type": "package", "compile": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "Grpc.AspNetCore/2.71.0": {"type": "package", "dependencies": {"Google.Protobuf": "3.30.2", "Grpc.AspNetCore.Server.ClientFactory": "2.71.0", "Grpc.Tools": "2.71.0"}, "compile": {"lib/net9.0/_._": {}}, "runtime": {"lib/net9.0/_._": {}}}, "Grpc.AspNetCore.Server/2.71.0": {"type": "package", "dependencies": {"Grpc.Net.Common": "2.71.0"}, "compile": {"lib/net9.0/Grpc.AspNetCore.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"type": "package", "dependencies": {"Grpc.AspNetCore.Server": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0"}, "compile": {"lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Grpc.Core.Api/2.71.0": {"type": "package", "compile": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Client/2.71.0": {"type": "package", "dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "compile": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.ClientFactory/2.71.0": {"type": "package", "dependencies": {"Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Http": "6.0.0"}, "compile": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"related": ".pdb;.xml"}}}, "Grpc.Net.Common/2.71.0": {"type": "package", "dependencies": {"Grpc.Core.Api": "2.71.0"}, "compile": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"related": ".pdb;.xml"}}}, "Grpc.Tools/2.72.0": {"type": "package", "build": {"build/_._": {}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Json.More.Net/2.1.0": {"type": "package", "compile": {"lib/net9.0/Json.More.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Json.More.dll": {"related": ".xml"}}}, "JsonPatch.Net/3.3.0": {"type": "package", "dependencies": {"JsonPointer.Net": "5.2.0"}, "compile": {"lib/net9.0/JsonPatch.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/JsonPatch.Net.dll": {"related": ".xml"}}}, "JsonPointer.Net/5.2.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}, "compile": {"lib/net9.0/JsonPointer.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/JsonPointer.Net.dll": {"related": ".xml"}}}, "KubernetesClient/16.0.7": {"type": "package", "dependencies": {"Fractions": "7.3.0", "YamlDotNet": "16.3.0"}, "compile": {"lib/net9.0/KubernetesClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/KubernetesClient.dll": {"related": ".pdb;.xml"}}}, "MessagePack/2.5.192": {"type": "package", "dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}, "compile": {"lib/net6.0/MessagePack.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MessagePack.dll": {"related": ".xml"}}}, "MessagePack.Annotations/2.5.192": {"type": "package", "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Configuration.CommandLine": "9.0.4", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Logging.Console": "9.0.4", "Microsoft.Extensions.Logging.Debug": "9.0.4", "Microsoft.Extensions.Logging.EventLog": "9.0.4", "Microsoft.Extensions.Logging.EventSource": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Diagnostics.EventLog": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "compile": {"ref/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "compile": {"lib/net8.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "compile": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Nerdbank.Streams/2.11.90": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "System.IO.Pipelines": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/net6.0/Nerdbank.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Nerdbank.Streams.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "Polly.Core/8.5.2": {"type": "package", "compile": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}}, "SonarAnalyzer.CSharp/10.12.0.118525": {"type": "package"}, "StreamJsonRpc/2.21.69": {"type": "package", "dependencies": {"MessagePack": "2.5.192", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.90", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/StreamJsonRpc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/StreamJsonRpc.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/StreamJsonRpc.resources.dll": {"locale": "cs"}, "lib/net8.0/de/StreamJsonRpc.resources.dll": {"locale": "de"}, "lib/net8.0/es/StreamJsonRpc.resources.dll": {"locale": "es"}, "lib/net8.0/fr/StreamJsonRpc.resources.dll": {"locale": "fr"}, "lib/net8.0/it/StreamJsonRpc.resources.dll": {"locale": "it"}, "lib/net8.0/ja/StreamJsonRpc.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/StreamJsonRpc.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/StreamJsonRpc.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/StreamJsonRpc.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/StreamJsonRpc.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Hashing/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.IO.Hashing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IO.Hashing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"type": "package", "compile": {"lib/net7.0/_._": {}}, "runtime": {"lib/net7.0/_._": {}}, "build": {"buildTransitive/net6.0/_._": {}}}, "YamlDotNet/16.3.0": {"type": "package", "compile": {"lib/net8.0/YamlDotNet.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/YamlDotNet.dll": {"related": ".xml"}}}}}, "libraries": {"Aspire.Dashboard.Sdk.win-x64/9.3.1": {"sha512": "0BAjVEMMBOkGaxVK3AioTiJKN2nyB+0hjvf8k2ek9LUIMowE2WvQfGUZhdZJaqmfqUCMdmFuhqNzkgubclIcXQ==", "type": "package", "path": "aspire.dashboard.sdk.win-x64/9.3.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.md", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "aspire.dashboard.sdk.win-x64.9.3.1.nupkg.sha512", "aspire.dashboard.sdk.win-x64.nuspec", "build/Aspire.Dashboard.Sdk.win-x64.props", "build/Aspire.Dashboard.Sdk.win-x64.targets", "buildMultiTargeting/Aspire.Dashboard.Sdk.win-x64.props", "buildMultiTargeting/Aspire.Dashboard.Sdk.win-x64.targets", "buildTransitive/Aspire.Dashboard.Sdk.win-x64.props", "buildTransitive/Aspire.Dashboard.Sdk.win-x64.targets", "tools/Aspire.Dashboard.deps.json", "tools/Aspire.Dashboard.dll", "tools/Aspire.Dashboard.exe", "tools/Aspire.Dashboard.runtimeconfig.json", "tools/Aspire.Dashboard.staticwebassets.endpoints.json", "tools/Aspire.Dashboard.xml", "tools/Google.Protobuf.dll", "tools/Grpc.AspNetCore.Server.ClientFactory.dll", "tools/Grpc.AspNetCore.Server.dll", "tools/Grpc.Core.Api.dll", "tools/Grpc.Net.Client.dll", "tools/Grpc.Net.ClientFactory.dll", "tools/Grpc.Net.Common.dll", "tools/Humanizer.dll", "tools/Markdig.dll", "tools/Microsoft.AspNetCore.Authentication.Certificate.dll", "tools/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "tools/Microsoft.AspNetCore.Authorization.dll", "tools/Microsoft.AspNetCore.Components.Forms.dll", "tools/Microsoft.AspNetCore.Components.Web.dll", "tools/Microsoft.AspNetCore.Components.dll", "tools/Microsoft.AspNetCore.Metadata.dll", "tools/Microsoft.Extensions.AI.Abstractions.dll", "tools/Microsoft.Extensions.AI.OpenAI.dll", "tools/Microsoft.Extensions.AI.dll", "tools/Microsoft.Extensions.Caching.Memory.dll", "tools/Microsoft.Extensions.Configuration.Binder.dll", "tools/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/Microsoft.Extensions.DependencyInjection.dll", "tools/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/Microsoft.Extensions.Diagnostics.dll", "tools/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/Microsoft.Extensions.Http.dll", "tools/Microsoft.Extensions.Logging.Abstractions.dll", "tools/Microsoft.Extensions.Logging.dll", "tools/Microsoft.Extensions.Options.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.Icons.Color.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.Icons.Filled.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.Icons.Light.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.Icons.dll", "tools/Microsoft.FluentUI.AspNetCore.Components.dll", "tools/Microsoft.IdentityModel.Abstractions.dll", "tools/Microsoft.IdentityModel.JsonWebTokens.dll", "tools/Microsoft.IdentityModel.Logging.dll", "tools/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "tools/Microsoft.IdentityModel.Protocols.dll", "tools/Microsoft.IdentityModel.Tokens.dll", "tools/Microsoft.JSInterop.dll", "tools/OpenAI.dll", "tools/System.ClientModel.dll", "tools/System.IdentityModel.Tokens.Jwt.dll", "tools/System.Memory.Data.dll", "tools/appsettings.Development.json", "tools/appsettings.json", "tools/cs/Aspire.Dashboard.resources.dll", "tools/de/Aspire.Dashboard.resources.dll", "tools/es/Aspire.Dashboard.resources.dll", "tools/fr/Aspire.Dashboard.resources.dll", "tools/it/Aspire.Dashboard.resources.dll", "tools/ja/Aspire.Dashboard.resources.dll", "tools/ko/Aspire.Dashboard.resources.dll", "tools/pl/Aspire.Dashboard.resources.dll", "tools/pt-BR/Aspire.Dashboard.resources.dll", "tools/ru/Aspire.Dashboard.resources.dll", "tools/tr/Aspire.Dashboard.resources.dll", "tools/web.config", "tools/wwwroot/Aspire.Dashboard.modules.json", "tools/wwwroot/Aspire.Dashboard.styles.css", "tools/wwwroot/Components/Controls/AssistantChat.razor.js", "tools/wwwroot/Components/Controls/Chart/MetricTable.razor.js", "tools/wwwroot/Components/Dialogs/TextVisualizerDialog.razor.js", "tools/wwwroot/Components/Pages/Login.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.n2s0c9bbw5.bundle.scp.css", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "tools/wwwroot/_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "tools/wwwroot/css/app.css", "tools/wwwroot/css/highlight.css", "tools/wwwroot/favicon.ico", "tools/wwwroot/img/TokenExample.png", "tools/wwwroot/js/app-metrics.js", "tools/wwwroot/js/app-resourcegraph.js", "tools/wwwroot/js/app-theme.js", "tools/wwwroot/js/app.js", "tools/wwwroot/js/d3.v7.min.js", "tools/wwwroot/js/highlight-11.10.0.min.js", "tools/wwwroot/js/plotly-basic-2.35.2.min.js", "tools/zh-Hans/Aspire.Dashboard.resources.dll", "tools/zh-Hant/Aspire.Dashboard.resources.dll"]}, "Aspire.Hosting/9.3.1": {"sha512": "9yVRi8TFV2VQD/7UFT2RezaPKa/91JisKIADAyrkaqJzNFBRQbh/z7xi4MQ6Bzugav/atOdAx4K/NCBdC6LZTg==", "type": "package", "path": "aspire.hosting/9.3.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "aspire.hosting.9.3.1.nupkg.sha512", "aspire.hosting.nuspec", "lib/net8.0/Aspire.Hosting.dll", "lib/net8.0/Aspire.Hosting.xml", "lib/net8.0/cs/Aspire.Hosting.resources.dll", "lib/net8.0/de/Aspire.Hosting.resources.dll", "lib/net8.0/es/Aspire.Hosting.resources.dll", "lib/net8.0/fr/Aspire.Hosting.resources.dll", "lib/net8.0/it/Aspire.Hosting.resources.dll", "lib/net8.0/ja/Aspire.Hosting.resources.dll", "lib/net8.0/ko/Aspire.Hosting.resources.dll", "lib/net8.0/pl/Aspire.Hosting.resources.dll", "lib/net8.0/pt-BR/Aspire.Hosting.resources.dll", "lib/net8.0/ru/Aspire.Hosting.resources.dll", "lib/net8.0/tr/Aspire.Hosting.resources.dll", "lib/net8.0/zh-Hans/Aspire.Hosting.resources.dll", "lib/net8.0/zh-Hant/Aspire.Hosting.resources.dll"]}, "Aspire.Hosting.AppHost/9.3.1": {"sha512": "U7lENUUkk97FpGsu6BLi2OA9RNaHKriyLw/t6Q/YOZrsch3PoEH09Hs7UCHfZIErKT40BczHjOvGP0uebNU8Tw==", "type": "package", "path": "aspire.hosting.apphost/9.3.1", "files": [".nupkg.metadata", ".signature.p7s", "AspireAppHostConfiguration.json", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Aspire.Hosting.Analyzers.dll", "aspire.hosting.apphost.9.3.1.nupkg.sha512", "aspire.hosting.apphost.nuspec", "build/Aspire.Hosting.AppHost.props", "build/Aspire.Hosting.AppHost.targets", "buildMultiTargeting/Aspire.Hosting.AppHost.props", "buildMultiTargeting/Aspire.Hosting.AppHost.targets", "lib/net8.0/Aspire.Hosting.AppHost.dll", "lib/net8.0/Aspire.Hosting.AppHost.xml", "lib/net9.0/Aspire.Hosting.AppHost.dll", "lib/net9.0/Aspire.Hosting.AppHost.xml"]}, "Aspire.Hosting.Orchestration.win-x64/9.3.1": {"sha512": "8tA+G0AHRF2DCg0WVwvXTwDGZYXetRlG9E+5UIfdB10lj6MqOSEVr6/1h9lIAQuTwy8HWtI7o8GyDotKg5AWeQ==", "type": "package", "path": "aspire.hosting.orchestration.win-x64/9.3.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "aspire.hosting.orchestration.win-x64.9.3.1.nupkg.sha512", "aspire.hosting.orchestration.win-x64.nuspec", "build/Aspire.Hosting.Orchestration.win-x64.targets", "buildMultiTargeting/Aspire.Hosting.Orchestration.win-x64.targets", "buildTransitive/Aspire.Hosting.Orchestration.win-x64.targets", "tools/EULA.rtf", "tools/NOTICE", "tools/dcp.exe", "tools/ext/bin/dcpproc.exe", "tools/ext/dcpctrl.exe"]}, "Aspire.Hosting.PostgreSQL/9.3.1": {"sha512": "YYmCU6zZCoaKCDLzQStgO4kr2sBhUGfMBXkR+SNpi93bFfq2lK3pPAR7EYM0kY+cCq29efUU82j8AGTREfg7nw==", "type": "package", "path": "aspire.hosting.postgresql/9.3.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "aspire.hosting.postgresql.9.3.1.nupkg.sha512", "aspire.hosting.postgresql.nuspec", "lib/net8.0/Aspire.Hosting.PostgreSQL.dll", "lib/net8.0/Aspire.Hosting.PostgreSQL.xml"]}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"sha512": "npc58/AD5zuVxERdhCl2Kb7WnL37mwX42SJcXIwvmEig0/dugOLg3SIwtfvvh3TnvTwR/sk5LYNkkPaBdks61A==", "type": "package", "path": "aspnetcore.healthchecks.npgsql/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "aspnetcore.healthchecks.npgsql.9.0.0.nupkg.sha512", "aspnetcore.healthchecks.npgsql.nuspec", "icon.png", "lib/net8.0/HealthChecks.NpgSql.dll", "lib/net8.0/HealthChecks.NpgSql.xml", "lib/netstandard2.0/HealthChecks.NpgSql.dll", "lib/netstandard2.0/HealthChecks.NpgSql.xml"]}, "AspNetCore.HealthChecks.Uris/9.0.0": {"sha512": "XYdNlA437KeF8p9qOpZFyNqAN+c0FXt/JjTvzH/Qans0q0O3pPE8KPnn39ucQQjR/Roum1vLTP3kXiUs8VHyuA==", "type": "package", "path": "aspnetcore.healthchecks.uris/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512", "aspnetcore.healthchecks.uris.nuspec", "icon.png", "lib/net8.0/HealthChecks.Uris.dll", "lib/net8.0/HealthChecks.Uris.xml", "lib/netstandard2.0/HealthChecks.Uris.dll", "lib/netstandard2.0/HealthChecks.Uris.xml"]}, "Fractions/7.3.0": {"sha512": "2bETFWLBc8b7Ut2SVi+bxhGVwiSpknHYGBh2PADyGWONLkTxT7bKyDRhF8ao+XUv90tq8Fl7GTPxSI5bacIRJw==", "type": "package", "path": "fractions/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Fraction.png", "Readme.md", "fractions.7.3.0.nupkg.sha512", "fractions.nuspec", "lib/netstandard2.0/Fractions.dll", "lib/netstandard2.0/Fractions.xml", "lib/netstandard2.1/Fractions.dll", "lib/netstandard2.1/Fractions.xml", "license.txt"]}, "Google.Protobuf/3.30.2": {"sha512": "Y2aOVLIt75yeeEWigg9V9YnjsEm53sADtLGq0gLhwaXpk3iu8tYSoauolyhenagA2sWno2TQ2WujI0HQd6s1Vw==", "type": "package", "path": "google.protobuf/3.30.2", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.30.2.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "Grpc.AspNetCore/2.71.0": {"sha512": "B4wAbNtAuHNiHAMxLFWL74wUElzNOOboFnypalqpX76piCOGz/w5FpilbVVYGboI4Qgl4ZmZsvDZ1zLwHNsjnw==", "type": "package", "path": "grpc.aspnetcore/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.aspnetcore.2.71.0.nupkg.sha512", "grpc.aspnetcore.nuspec", "lib/net6.0/_._", "lib/net7.0/_._", "lib/net8.0/_._", "lib/net9.0/_._", "packageIcon.png"]}, "Grpc.AspNetCore.Server/2.71.0": {"sha512": "kv+9YVB6MqDYWIcstXvWrT7Xc1si/sfINzzSxvQfjC3aei+92gXDUXCH/Q+TEvi4QSICRqu92BYcrXUBW7cuOw==", "type": "package", "path": "grpc.aspnetcore.server/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.aspnetcore.server.2.71.0.nupkg.sha512", "grpc.aspnetcore.server.nuspec", "lib/net6.0/Grpc.AspNetCore.Server.dll", "lib/net6.0/Grpc.AspNetCore.Server.pdb", "lib/net6.0/Grpc.AspNetCore.Server.xml", "lib/net7.0/Grpc.AspNetCore.Server.dll", "lib/net7.0/Grpc.AspNetCore.Server.pdb", "lib/net7.0/Grpc.AspNetCore.Server.xml", "lib/net8.0/Grpc.AspNetCore.Server.dll", "lib/net8.0/Grpc.AspNetCore.Server.pdb", "lib/net8.0/Grpc.AspNetCore.Server.xml", "lib/net9.0/Grpc.AspNetCore.Server.dll", "lib/net9.0/Grpc.AspNetCore.Server.pdb", "lib/net9.0/Grpc.AspNetCore.Server.xml", "packageIcon.png"]}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"sha512": "AHvMxoC+esO1e/nOYBjxvn0WDHAfglcVBjtkBy6ohgnV+PzkF8UdkPHE02xnyPFaSokWGZKnWzjgd00x6EZpyQ==", "type": "package", "path": "grpc.aspnetcore.server.clientfactory/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512", "grpc.aspnetcore.server.clientfactory.nuspec", "lib/net6.0/Grpc.AspNetCore.Server.ClientFactory.dll", "lib/net6.0/Grpc.AspNetCore.Server.ClientFactory.pdb", "lib/net6.0/Grpc.AspNetCore.Server.ClientFactory.xml", "lib/net7.0/Grpc.AspNetCore.Server.ClientFactory.dll", "lib/net7.0/Grpc.AspNetCore.Server.ClientFactory.pdb", "lib/net7.0/Grpc.AspNetCore.Server.ClientFactory.xml", "lib/net8.0/Grpc.AspNetCore.Server.ClientFactory.dll", "lib/net8.0/Grpc.AspNetCore.Server.ClientFactory.pdb", "lib/net8.0/Grpc.AspNetCore.Server.ClientFactory.xml", "lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll", "lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.pdb", "lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.xml", "packageIcon.png"]}, "Grpc.Core.Api/2.71.0": {"sha512": "QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "type": "package", "path": "grpc.core.api/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.core.api.2.71.0.nupkg.sha512", "grpc.core.api.nuspec", "lib/net462/Grpc.Core.Api.dll", "lib/net462/Grpc.Core.Api.pdb", "lib/net462/Grpc.Core.Api.xml", "lib/netstandard2.0/Grpc.Core.Api.dll", "lib/netstandard2.0/Grpc.Core.Api.pdb", "lib/netstandard2.0/Grpc.Core.Api.xml", "lib/netstandard2.1/Grpc.Core.Api.dll", "lib/netstandard2.1/Grpc.Core.Api.pdb", "lib/netstandard2.1/Grpc.Core.Api.xml", "packageIcon.png"]}, "Grpc.Net.Client/2.71.0": {"sha512": "U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "type": "package", "path": "grpc.net.client/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.net.client.2.71.0.nupkg.sha512", "grpc.net.client.nuspec", "lib/net462/Grpc.Net.Client.dll", "lib/net462/Grpc.Net.Client.pdb", "lib/net462/Grpc.Net.Client.xml", "lib/net6.0/Grpc.Net.Client.dll", "lib/net6.0/Grpc.Net.Client.pdb", "lib/net6.0/Grpc.Net.Client.xml", "lib/net7.0/Grpc.Net.Client.dll", "lib/net7.0/Grpc.Net.Client.pdb", "lib/net7.0/Grpc.Net.Client.xml", "lib/net8.0/Grpc.Net.Client.dll", "lib/net8.0/Grpc.Net.Client.pdb", "lib/net8.0/Grpc.Net.Client.xml", "lib/netstandard2.0/Grpc.Net.Client.dll", "lib/netstandard2.0/Grpc.Net.Client.pdb", "lib/netstandard2.0/Grpc.Net.Client.xml", "lib/netstandard2.1/Grpc.Net.Client.dll", "lib/netstandard2.1/Grpc.Net.Client.pdb", "lib/netstandard2.1/Grpc.Net.Client.xml", "packageIcon.png"]}, "Grpc.Net.ClientFactory/2.71.0": {"sha512": "8oPLwQLPo86fmcf9ghjCDyNsSWhtHc3CXa/AqwF8Su/pG7qAoeWWtbymsZhoNvCV9Zjzb6BDcIPKXLYt+O175g==", "type": "package", "path": "grpc.net.clientfactory/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "grpc.net.clientfactory.2.71.0.nupkg.sha512", "grpc.net.clientfactory.nuspec", "lib/net6.0/Grpc.Net.ClientFactory.dll", "lib/net6.0/Grpc.Net.ClientFactory.pdb", "lib/net6.0/Grpc.Net.ClientFactory.xml", "lib/net7.0/Grpc.Net.ClientFactory.dll", "lib/net7.0/Grpc.Net.ClientFactory.pdb", "lib/net7.0/Grpc.Net.ClientFactory.xml", "lib/net8.0/Grpc.Net.ClientFactory.dll", "lib/net8.0/Grpc.Net.ClientFactory.pdb", "lib/net8.0/Grpc.Net.ClientFactory.xml", "lib/netstandard2.0/Grpc.Net.ClientFactory.dll", "lib/netstandard2.0/Grpc.Net.ClientFactory.pdb", "lib/netstandard2.0/Grpc.Net.ClientFactory.xml", "lib/netstandard2.1/Grpc.Net.ClientFactory.dll", "lib/netstandard2.1/Grpc.Net.ClientFactory.pdb", "lib/netstandard2.1/Grpc.Net.ClientFactory.xml", "packageIcon.png"]}, "Grpc.Net.Common/2.71.0": {"sha512": "v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "type": "package", "path": "grpc.net.common/2.71.0", "files": [".nupkg.metadata", ".signature.p7s", "grpc.net.common.2.71.0.nupkg.sha512", "grpc.net.common.nuspec", "lib/net6.0/Grpc.Net.Common.dll", "lib/net6.0/Grpc.Net.Common.pdb", "lib/net6.0/Grpc.Net.Common.xml", "lib/net7.0/Grpc.Net.Common.dll", "lib/net7.0/Grpc.Net.Common.pdb", "lib/net7.0/Grpc.Net.Common.xml", "lib/net8.0/Grpc.Net.Common.dll", "lib/net8.0/Grpc.Net.Common.pdb", "lib/net8.0/Grpc.Net.Common.xml", "lib/netstandard2.0/Grpc.Net.Common.dll", "lib/netstandard2.0/Grpc.Net.Common.pdb", "lib/netstandard2.0/Grpc.Net.Common.xml", "lib/netstandard2.1/Grpc.Net.Common.dll", "lib/netstandard2.1/Grpc.Net.Common.pdb", "lib/netstandard2.1/Grpc.Net.Common.xml", "packageIcon.png"]}, "Grpc.Tools/2.72.0": {"sha512": "BCiuQ03EYjLHCo9hqZmY5barsz5vvcz/+/ICt5wCbukaePHZmMPDGelKlkxWx3q+f5xOMNHa9zXQ2N6rQZ4B+w==", "type": "package", "path": "grpc.tools/2.72.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Grpc.Tools.props", "build/Grpc.Tools.targets", "build/_grpc/Grpc.CSharp.xml", "build/_grpc/_Grpc.Tools.props", "build/_grpc/_Grpc.Tools.targets", "build/_protobuf/Google.Protobuf.Tools.props", "build/_protobuf/Google.Protobuf.Tools.targets", "build/_protobuf/Protobuf.CSharp.xml", "build/_protobuf/net45/Protobuf.MSBuild.dll", "build/_protobuf/net45/Protobuf.MSBuild.pdb", "build/_protobuf/netstandard1.3/Protobuf.MSBuild.dll", "build/_protobuf/netstandard1.3/Protobuf.MSBuild.pdb", "build/native/include/google/protobuf/any.proto", "build/native/include/google/protobuf/api.proto", "build/native/include/google/protobuf/descriptor.proto", "build/native/include/google/protobuf/duration.proto", "build/native/include/google/protobuf/empty.proto", "build/native/include/google/protobuf/field_mask.proto", "build/native/include/google/protobuf/source_context.proto", "build/native/include/google/protobuf/struct.proto", "build/native/include/google/protobuf/timestamp.proto", "build/native/include/google/protobuf/type.proto", "build/native/include/google/protobuf/wrappers.proto", "grpc.tools.2.72.0.nupkg.sha512", "grpc.tools.nuspec", "packageIcon.png", "tools/linux_arm64/grpc_csharp_plugin", "tools/linux_arm64/protoc", "tools/linux_x64/grpc_csharp_plugin", "tools/linux_x64/protoc", "tools/linux_x86/grpc_csharp_plugin", "tools/linux_x86/protoc", "tools/macosx_x64/grpc_csharp_plugin", "tools/macosx_x64/protoc", "tools/windows_x64/grpc_csharp_plugin.exe", "tools/windows_x64/protoc.exe", "tools/windows_x86/grpc_csharp_plugin.exe", "tools/windows_x86/protoc.exe"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Json.More.Net/2.1.0": {"sha512": "qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg==", "type": "package", "path": "json.more.net/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "json-logo-256.png", "json.more.net.2.1.0.nupkg.sha512", "json.more.net.nuspec", "lib/net8.0/Json.More.dll", "lib/net8.0/Json.More.xml", "lib/net9.0/Json.More.dll", "lib/net9.0/Json.More.xml", "lib/netstandard2.0/Json.More.dll", "lib/netstandard2.0/Json.More.xml"]}, "JsonPatch.Net/3.3.0": {"sha512": "GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "type": "package", "path": "jsonpatch.net/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "json-logo-256.png", "jsonpatch.net.3.3.0.nupkg.sha512", "jsonpatch.net.nuspec", "lib/net8.0/JsonPatch.Net.dll", "lib/net8.0/JsonPatch.Net.xml", "lib/net9.0/JsonPatch.Net.dll", "lib/net9.0/JsonPatch.Net.xml", "lib/netstandard2.0/JsonPatch.Net.dll", "lib/netstandard2.0/JsonPatch.Net.xml"]}, "JsonPointer.Net/5.2.0": {"sha512": "qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "type": "package", "path": "jsonpointer.net/5.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "json-logo-256.png", "jsonpointer.net.5.2.0.nupkg.sha512", "jsonpointer.net.nuspec", "lib/net8.0/JsonPointer.Net.dll", "lib/net8.0/JsonPointer.Net.xml", "lib/net9.0/JsonPointer.Net.dll", "lib/net9.0/JsonPointer.Net.xml", "lib/netstandard2.0/JsonPointer.Net.dll", "lib/netstandard2.0/JsonPointer.Net.xml"]}, "KubernetesClient/16.0.7": {"sha512": "hH+YN18bpIRO/rq2CiMGDpLpc/KjSMlAn4EelFB4PgiswbSie4jANLAOou1Q39Kx7en2jO1Qp73y3SkjxGJIMg==", "type": "package", "path": "kubernetesclient/16.0.7", "files": [".nupkg.metadata", ".signature.p7s", "kubernetesclient.16.0.7.nupkg.sha512", "kubernetesclient.nuspec", "lib/net8.0/KubernetesClient.dll", "lib/net8.0/KubernetesClient.pdb", "lib/net8.0/KubernetesClient.xml", "lib/net9.0/KubernetesClient.dll", "lib/net9.0/KubernetesClient.pdb", "lib/net9.0/KubernetesClient.xml", "logo.png"]}, "MessagePack/2.5.192": {"sha512": "Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "type": "package", "path": "messagepack/2.5.192", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/MessagePack.dll", "lib/net472/MessagePack.xml", "lib/net6.0/MessagePack.dll", "lib/net6.0/MessagePack.xml", "lib/netstandard2.0/MessagePack.dll", "lib/netstandard2.0/MessagePack.xml", "messagepack.2.5.192.nupkg.sha512", "messagepack.nuspec"]}, "MessagePack.Annotations/2.5.192": {"sha512": "jaJuwcgovWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg==", "type": "package", "path": "messagepack.annotations/2.5.192", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/MessagePack.Annotations.dll", "lib/netstandard2.0/MessagePack.Annotations.xml", "messagepack.annotations.2.5.192.nupkg.sha512", "messagepack.annotations.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.4": {"sha512": "KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "type": "package", "path": "microsoft.extensions.configuration/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"sha512": "0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"sha512": "cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"sha512": "TbM2HElARG7z1gxwakdppmOkm1SykPqDcu3EF97daEwSb/+TXnRrFfJtF+5FWWxcsNhbRrmLfS2WszYcab7u1A==", "type": "package", "path": "microsoft.extensions.configuration.commandline/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"sha512": "2IGiG3FtVnD83IA6HYGuNei8dOw455C09yEhGl8bjcY6aGZgoC6yhYvDnozw8wlTowfoG9bxVrdTsr2ACZOYHg==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"sha512": "UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"sha512": "vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"sha512": "zuvyC72gJkJyodyGowCuz3EQ1QvzNXJtKusuRzmjoHr17aeB3X0aSiKFB++HMHnQIWWlPOBf9YHTQfEqzbgl1g==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"sha512": "f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"sha512": "UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/9.0.4": {"sha512": "1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"sha512": "IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"sha512": "jW9lhWQzOOL5sBUCNtAiS6B7tGeLlxJVDjwNuQAQl6dDt9PAAxt3+T2F2jtcvi7KoujgzAdkKQKtGoRaAGlD9w==", "type": "package", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Diagnostics.HealthChecks.dll", "lib/net462/Microsoft.Extensions.Diagnostics.HealthChecks.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.xml", "microsoft.extensions.diagnostics.healthchecks.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.healthchecks.nuspec"]}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"sha512": "XM6WwNbDkVuGhDN89eKxA2Og2eMDXB0PVI7PEzl2R0MbFjYUlfTh7D7vBPEWUVCf2zPDAFiwcMlnVzi6Umq5mg==", "type": "package", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.xml", "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.healthchecks.abstractions.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"sha512": "gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"sha512": "qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"sha512": "05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/9.0.4": {"sha512": "1rZwLE+tTUIyZRUzmlk/DQj+v+Eqox+rjb+X7Fi+cYTbQfIZPYwpf1pVybsV3oje8+Pe4GaNukpBVUlPYeQdeQ==", "type": "package", "path": "microsoft.extensions.hosting/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.9.0.4.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"sha512": "bXkwRPMo4x19YKH6/V9XotU7KYQJlihXhcWO1RDclAY3yfY3XNg4QtSEBvng4kK/DnboE0O/nwSl+6Jiv9P+FA==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/9.0.4": {"sha512": "ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "type": "package", "path": "microsoft.extensions.http/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/net9.0/Microsoft.Extensions.Http.dll", "lib/net9.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.9.0.4.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.4": {"sha512": "xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "type": "package", "path": "microsoft.extensions.logging/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.4.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"sha512": "0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"sha512": "/kF+rSnoo3/nIwGzWsR4RgBnoTOdZ3lzz2qFRyp/GgaNid4j6hOAQrs/O+QHXhlcAdZxjg37MvtIE+pAvIgi9g==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.4.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.4": {"sha512": "cI0lQe0js65INCTCtAgnlVJWKgzgoRHVAW1B1zwCbmcliO4IZoTf92f1SYbLeLk7FzMJ/GlCvjLvJegJ6kltmQ==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.4.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"sha512": "D1jy+jy+huUUxnkZ0H480RZK8vqKn8NsQxYpMpPL/ALPPh1WATVLcr/uXI3RUBB45wMW5265O+hk9x3jnnXFuA==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.4.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"sha512": "bApxdklf7QTsONOLR5ow6SdDFXR5ncHvumSEg2+QnCvxvkzc2z5kNn7yQCyupRLRN4jKbnlTkVX8x9qLlwL6Qg==", "type": "package", "path": "microsoft.extensions.logging.eventlog/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.9.0.4.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"sha512": "R600zTxVJNw2IeAEOvdOJGNA1lHr1m3vo460hSF5G1DjwP0FNpyeH4lpLDMuf34diKwB1LTt5hBw1iF1/iuwsQ==", "type": "package", "path": "microsoft.extensions.logging.eventsource/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.9.0.4.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.4": {"sha512": "fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "type": "package", "path": "microsoft.extensions.options/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.4.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"sha512": "aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.4": {"sha512": "SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.4.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NET.StringTools/17.6.3": {"sha512": "N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "type": "package", "path": "microsoft.net.stringtools/17.6.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.NET.StringTools.dll", "lib/net472/Microsoft.NET.StringTools.pdb", "lib/net472/Microsoft.NET.StringTools.xml", "lib/net7.0/Microsoft.NET.StringTools.dll", "lib/net7.0/Microsoft.NET.StringTools.pdb", "lib/net7.0/Microsoft.NET.StringTools.xml", "lib/netstandard2.0/Microsoft.NET.StringTools.dll", "lib/netstandard2.0/Microsoft.NET.StringTools.pdb", "lib/netstandard2.0/Microsoft.NET.StringTools.xml", "microsoft.net.stringtools.17.6.3.nupkg.sha512", "microsoft.net.stringtools.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.NET.StringTools.dll", "ref/net472/Microsoft.NET.StringTools.xml", "ref/net7.0/Microsoft.NET.StringTools.dll", "ref/net7.0/Microsoft.NET.StringTools.xml", "ref/netstandard2.0/Microsoft.NET.StringTools.dll", "ref/netstandard2.0/Microsoft.NET.StringTools.xml"]}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"sha512": "vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "type": "package", "path": "microsoft.visualstudio.threading.only/17.13.61", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "lib/net472/Microsoft.VisualStudio.Threading.dll", "lib/net472/Microsoft.VisualStudio.Threading.xml", "lib/net472/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/manifest.spdx.json", "lib/net472/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.dll", "lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.xml", "lib/net8.0-windows7.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/manifest.spdx.json", "lib/net8.0-windows7.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/Microsoft.VisualStudio.Threading.dll", "lib/net8.0/Microsoft.VisualStudio.Threading.xml", "lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/manifest.spdx.json", "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/manifest.spdx.json", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "microsoft.visualstudio.threading.only.nuspec"]}, "Microsoft.VisualStudio.Validation/17.8.8": {"sha512": "rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "type": "package", "path": "microsoft.visualstudio.validation/17.8.8", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "lib/net6.0/Microsoft.VisualStudio.Validation.dll", "lib/net6.0/Microsoft.VisualStudio.Validation.xml", "lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "microsoft.visualstudio.validation.nuspec"]}, "Nerdbank.Streams/2.11.90": {"sha512": "7jrOfU6b/PVBccqzNLfw9u84WWzkSpvWLb2mZxvwdQkOx/V9FXWkmnp/rjOnBFDOhrO/ev4+gQ5QS13FkgNSBA==", "type": "package", "path": "nerdbank.streams/2.11.90", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "README.md", "lib/net6.0/Nerdbank.Streams.dll", "lib/net6.0/Nerdbank.Streams.xml", "lib/netstandard2.0/Nerdbank.Streams.dll", "lib/netstandard2.0/Nerdbank.Streams.xml", "lib/netstandard2.1/Nerdbank.Streams.dll", "lib/netstandard2.1/Nerdbank.Streams.xml", "nerdbank.streams.2.11.90.nupkg.sha512", "nerdbank.streams.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Npgsql/8.0.3": {"sha512": "6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "type": "package", "path": "npgsql/8.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net7.0/Npgsql.dll", "lib/net7.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.8.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Polly.Core/8.5.2": {"sha512": "1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw==", "type": "package", "path": "polly.core/8.5.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Core.dll", "lib/net462/Polly.Core.pdb", "lib/net462/Polly.Core.xml", "lib/net472/Polly.Core.dll", "lib/net472/Polly.Core.pdb", "lib/net472/Polly.Core.xml", "lib/net6.0/Polly.Core.dll", "lib/net6.0/Polly.Core.pdb", "lib/net6.0/Polly.Core.xml", "lib/net8.0/Polly.Core.dll", "lib/net8.0/Polly.Core.pdb", "lib/net8.0/Polly.Core.xml", "lib/netstandard2.0/Polly.Core.dll", "lib/netstandard2.0/Polly.Core.pdb", "lib/netstandard2.0/Polly.Core.xml", "package-icon.png", "package-readme.md", "polly.core.8.5.2.nupkg.sha512", "polly.core.nuspec"]}, "SonarAnalyzer.CSharp/10.12.0.118525": {"sha512": "uP38bsYegQBk8WOM6LYIAht6hrA7tcJgep/WuifPJjhjtjysPUP/iM/c1+P2+llNIDmm1s8Xh86+WG3K71eycw==", "type": "package", "path": "sonaranalyzer.csharp/10.12.0.118525", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "analyzers/SonarAnalyzer.CSharp.dll", "images/sonarsource_64.png", "license/THIRD-PARTY-NOTICES.txt", "sonaranalyzer.csharp.10.12.0.118525.nupkg.sha512", "sonaranalyzer.csharp.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "StreamJsonRpc/2.21.69": {"sha512": "WbTpn/PIo+HpFYnsOCiOOe0kHUE2N1eiVRi7MO70DFBTMG3pAOfrgHtwUpOJ37dfDETq/9P9WNIbHom4ABZfrA==", "type": "package", "path": "streamjsonrpc/2.21.69", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "lib/net8.0/StreamJsonRpc.dll", "lib/net8.0/StreamJsonRpc.xml", "lib/net8.0/cs/StreamJsonRpc.resources.dll", "lib/net8.0/de/StreamJsonRpc.resources.dll", "lib/net8.0/es/StreamJsonRpc.resources.dll", "lib/net8.0/fr/StreamJsonRpc.resources.dll", "lib/net8.0/it/StreamJsonRpc.resources.dll", "lib/net8.0/ja/StreamJsonRpc.resources.dll", "lib/net8.0/ko/StreamJsonRpc.resources.dll", "lib/net8.0/manifest.spdx.json", "lib/net8.0/pl/StreamJsonRpc.resources.dll", "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll", "lib/net8.0/ru/StreamJsonRpc.resources.dll", "lib/net8.0/tr/StreamJsonRpc.resources.dll", "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll", "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll", "lib/netstandard2.0/StreamJsonRpc.dll", "lib/netstandard2.0/StreamJsonRpc.xml", "lib/netstandard2.0/cs/StreamJsonRpc.resources.dll", "lib/netstandard2.0/de/StreamJsonRpc.resources.dll", "lib/netstandard2.0/es/StreamJsonRpc.resources.dll", "lib/netstandard2.0/fr/StreamJsonRpc.resources.dll", "lib/netstandard2.0/it/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ja/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ko/StreamJsonRpc.resources.dll", "lib/netstandard2.0/manifest.spdx.json", "lib/netstandard2.0/pl/StreamJsonRpc.resources.dll", "lib/netstandard2.0/pt-BR/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ru/StreamJsonRpc.resources.dll", "lib/netstandard2.0/tr/StreamJsonRpc.resources.dll", "lib/netstandard2.0/zh-<PERSON>/StreamJsonRpc.resources.dll", "lib/netstandard2.0/zh-Hant/StreamJsonRpc.resources.dll", "lib/netstandard2.1/StreamJsonRpc.dll", "lib/netstandard2.1/StreamJsonRpc.xml", "lib/netstandard2.1/cs/StreamJsonRpc.resources.dll", "lib/netstandard2.1/de/StreamJsonRpc.resources.dll", "lib/netstandard2.1/es/StreamJsonRpc.resources.dll", "lib/netstandard2.1/fr/StreamJsonRpc.resources.dll", "lib/netstandard2.1/it/StreamJsonRpc.resources.dll", "lib/netstandard2.1/ja/StreamJsonRpc.resources.dll", "lib/netstandard2.1/ko/StreamJsonRpc.resources.dll", "lib/netstandard2.1/manifest.spdx.json", "lib/netstandard2.1/pl/StreamJsonRpc.resources.dll", "lib/netstandard2.1/pt-BR/StreamJsonRpc.resources.dll", "lib/netstandard2.1/ru/StreamJsonRpc.resources.dll", "lib/netstandard2.1/tr/StreamJsonRpc.resources.dll", "lib/netstandard2.1/zh-<PERSON>/StreamJsonRpc.resources.dll", "lib/netstandard2.1/zh-Hant/StreamJsonRpc.resources.dll", "streamjsonrpc.2.21.69.nupkg.sha512", "streamjsonrpc.nuspec"]}, "System.Diagnostics.EventLog/9.0.4": {"sha512": "getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "type": "package", "path": "system.diagnostics.eventlog/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.4.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Hashing/9.0.4": {"sha512": "WogPvgAFqQORFD8Iyha6RZ+/1QB3dsWRWxbwi8/HHVgiGQ8z0oMWpwe8Kk3Ti+Roe+P6a3sBg+WwBfEsyziZKg==", "type": "package", "path": "system.io.hashing/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Hashing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Hashing.targets", "lib/net462/System.IO.Hashing.dll", "lib/net462/System.IO.Hashing.xml", "lib/net8.0/System.IO.Hashing.dll", "lib/net8.0/System.IO.Hashing.xml", "lib/net9.0/System.IO.Hashing.dll", "lib/net9.0/System.IO.Hashing.xml", "lib/netstandard2.0/System.IO.Hashing.dll", "lib/netstandard2.0/System.IO.Hashing.xml", "system.io.hashing.9.0.4.nupkg.sha512", "system.io.hashing.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"sha512": "5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "YamlDotNet/16.3.0": {"sha512": "SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "type": "package", "path": "yamldotnet/16.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "images/yamldotnet.png", "lib/net47/YamlDotNet.dll", "lib/net47/YamlDotNet.xml", "lib/net6.0/YamlDotNet.dll", "lib/net6.0/YamlDotNet.xml", "lib/net8.0/YamlDotNet.dll", "lib/net8.0/YamlDotNet.xml", "lib/netstandard2.0/YamlDotNet.dll", "lib/netstandard2.0/YamlDotNet.xml", "lib/netstandard2.1/YamlDotNet.dll", "lib/netstandard2.1/YamlDotNet.xml", "yamldotnet.16.3.0.nupkg.sha512", "yamldotnet.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["Aspire.Dashboard.Sdk.win-x64 >= 9.3.1", "Aspire.Hosting.AppHost >= 9.3.1", "Aspire.Hosting.Orchestration.win-x64 >= 9.3.1", "Aspire.Hosting.PostgreSQL >= 9.3.1", "SonarAnalyzer.CSharp >= 10.12.0.118525"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Aspire.AppHost\\Aspire.AppHost.csproj", "projectName": "Aspire.AppHost", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Aspire.AppHost\\Aspire.AppHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Aspire.AppHost\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspire.Dashboard.Sdk.win-x64": {"target": "Package", "version": "[9.3.1, )", "autoReferenced": true}, "Aspire.Hosting.AppHost": {"target": "Package", "version": "[9.3.1, )", "versionCentrallyManaged": true}, "Aspire.Hosting.Orchestration.win-x64": {"target": "Package", "version": "[9.3.1, )", "autoReferenced": true}, "Aspire.Hosting.PostgreSQL": {"target": "Package", "version": "[9.3.1, )", "versionCentrallyManaged": true}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.12.0.118525, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.1", "Aspire.Hosting.PostgreSQL": "9.3.1", "AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "CommunityToolkit.Aspire.Hosting.Ollama": "9.4.0", "CommunityToolkit.Aspire.OllamaSharp": "9.5.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentAssertions": "7.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Hangfire": "1.8.20", "Hangfire.AspNetCore": "1.8.20", "Hangfire.Core": "1.8.20", "Hangfire.PostgreSql": "1.20.12", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Microsoft.Extensions.AI": "9.7.1", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.ServiceDiscovery": "9.3.1", "Microsoft.NET.Test.Sdk": "17.14.1", "Microsoft.SemanticKernel.Connectors.Google": "1.60.0-alpha", "Microsoft.SemanticKernel.Plugins.Web": "1.61.0-alpha", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.OpenTelemetry": "9.0.3", "NSubstitute": "5.3.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.EntityFrameworkCore": "1.12.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0", "Scrutor": "6.1.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.12.0.118525", "Swashbuckle.AspNetCore": "9.0.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.1", "YamlDotNet": "16.2.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}