using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;
using System.Text.Json;

namespace Application.AI.ExecuteCompanyResearch;

internal sealed class ExecuteCompanyResearchCommandHandler 
    : ICommandHandler<ExecuteCompanyResearchCommand, CompanyResearchResponse>
{
    private readonly ICompanyResearchAgent _agent;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<ExecuteCompanyResearchCommandHandler> _logger;

    public ExecuteCompanyResearchCommandHandler(
        ICompanyResearchAgent agent,
        IApplicationDbContext dbContext,
        ILogger<ExecuteCompanyResearchCommandHandler> logger)
    {
        _agent = agent;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result<CompanyResearchResponse>> Handle(
        ExecuteCompanyResearchCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing company research for JobApplication {JobApplicationId}, Company: {CompanyUrl}",
            request.JobApplicationId, request.CompanyUrl);

        try
        {
            // Validate job application exists
            var jobApplication = await _dbContext.JobApplications
                .FirstOrDefaultAsync(ja => ja.Id == request.JobApplicationId, cancellationToken);

            if (jobApplication is null)
            {
                return Result.Failure<CompanyResearchResponse>(
                    Error.NotFound("ExecuteCompanyResearch.JobApplicationNotFound",
                        $"Job application with ID {request.JobApplicationId} was not found"));
            }

            // Create agent request
            var agentRequest = new CompanyResearchRequest(
                request.JobApplicationId,
                request.CompanyUrl,
                request.JobTitle,
                request.PreferredAIModel);

            // Execute agent
            var agentResult = await _agent.ProcessAsync(agentRequest, cancellationToken);

            if (agentResult.IsFailure)
            {
                _logger.LogError("Company research failed for JobApplication {JobApplicationId}: {Error}",
                    request.JobApplicationId, agentResult.Error.Description);
                return Result.Failure<CompanyResearchResponse>(agentResult.Error);
            }

            var response = agentResult.Value;

            // Update job application with results
            var companyData = JsonSerializer.Serialize(response);
            jobApplication.UpdateCompanyResearch(companyData, response.ConfidenceScore);

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Company research completed successfully for JobApplication {JobApplicationId} with confidence {Confidence}",
                request.JobApplicationId, response.ConfidenceScore);

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error executing company research for JobApplication {JobApplicationId}",
                request.JobApplicationId);

            return Result.Failure<CompanyResearchResponse>(
                Error.Problem("ExecuteCompanyResearch.UnexpectedError", ex.Message));
        }
    }
}
