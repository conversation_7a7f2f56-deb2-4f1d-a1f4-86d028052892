using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Data;
using Microsoft.SemanticKernel.Plugins.Web.Google;
using SharedKernel;
using System.Text.Json;

namespace Infrastructure.AI;

/// <summary>
/// Agent responsible for researching company information from URLs with web search capabilities
/// </summary>
internal sealed class CompanyResearchAgent : ICompanyResearchAgent
{
    private readonly IAIChatServiceFactory _chatServiceFactory;
    private readonly ICompanyResearchPromptService _promptService;
    private readonly ILogger<CompanyResearchAgent> _logger;
    private readonly IWebSearchService _webSearchService;

    public AgentType AgentType => AgentType.CompanyResearch;

    public CompanyResearchAgent(
        IAIChatServiceFactory chatServiceFactory,
        ICompanyResearchPromptService promptService,
        IWebSearchService webSearchService,
        ILogger<CompanyResearchAgent> logger)
    {
        _chatServiceFactory = chatServiceFactory;
        _promptService = promptService;
        _webSearchService = webSearchService;
        _logger = logger;
    }

    public async Task<Result<CompanyResearchResponse>> ProcessAsync(
        CompanyResearchRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting company research for JobApplication {JobApplicationId}, Company: {CompanyUrl}",
            request.JobApplicationId, request.CompanyUrl);

        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.CompanyUrl))
            {
                return Result.Failure<CompanyResearchResponse>(
                    Error.Problem("CompanyResearchAgent.InvalidUrl", "Company URL cannot be empty"));
            }

            if (!IsValidUrl(request.CompanyUrl))
            {
                return Result.Failure<CompanyResearchResponse>(
                    Error.Problem("CompanyResearchAgent.InvalidUrl", "Company URL format is invalid"));
            }

            // Perform web search to gather company information
            _logger.LogDebug("Performing web search for company information");
            var searchResult = await _webSearchService.SearchCompanyInformationAsync(
                request.CompanyUrl,
                request.JobTitle,
                cancellationToken);

            string searchContext = string.Empty;
            if (searchResult.IsSuccess)
            {
                searchContext = searchResult.Value.GetCombinedContent();
                _logger.LogDebug("Web search completed successfully with {ResultCount} results",
                    searchResult.Value.SearchResults.Count);
            }
            else
            {
                _logger.LogWarning("Web search failed: {Error}", searchResult.Error.Description);
                // Continue without search context
            }

            // Get chat completion service
            var chatServiceResult = _chatServiceFactory.CreateChatCompletionService(request.PreferredAIModel);
            if (chatServiceResult.IsFailure)
            {
                return Result.Failure<CompanyResearchResponse>(chatServiceResult.Error);
            }

            var chatCompletionService = chatServiceResult.Value;

            // Build prompts with search context
            var systemMessage = _promptService.GetSystemMessage();
            var userPrompt = _promptService.GetUserPromptWithSearchContext(
                request.CompanyUrl,
                request.JobTitle,
                searchContext);

            _logger.LogDebug("Sending company research request to AI service for URL: {CompanyUrl}", request.CompanyUrl);

            // Create chat history
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(systemMessage);
            chatHistory.AddUserMessage(userPrompt);

            // Create execution settings
            var executionSettings = new GeminiPromptExecutionSettings
            {
                ResponseMimeType = "application/json"
            };

            // Call AI service with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(3));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var response = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                cancellationToken: combinedCts.Token);

            if (response?.Content is null || string.IsNullOrEmpty(response.Content))
            {
                _logger.LogError("AI service returned empty response for company research");
                return Result.Failure<CompanyResearchResponse>(
                    Error.Problem("CompanyResearchAgent.EmptyResponse", "AI service returned empty response"));
            }

            // Parse AI response
            var researchResponse = ParseAIResponse(response.Content);

            // Validate response quality
            var qualityControl = _promptService.GetConfiguration().QualityControl;
            if (researchResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                _logger.LogWarning("Company research confidence {Confidence} is below threshold {Threshold}",
                    researchResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            _logger.LogInformation("Company research completed successfully for {CompanyName} with confidence {Confidence}",
                researchResponse.CompanyName, researchResponse.ConfidenceScore);

            return Result.Success(researchResponse);
        }
        catch (OperationCanceledException ex)
        {
            _logger.LogWarning(ex, "Company research was cancelled or timed out for URL: {CompanyUrl}", request.CompanyUrl);
            return Result.Failure<CompanyResearchResponse>(
                Error.Problem("CompanyResearchAgent.Timeout", "Company research timed out"));
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response as JSON for company research");
            return Result.Failure<CompanyResearchResponse>(
                Error.Problem("CompanyResearchAgent.InvalidResponse", "Failed to parse AI response"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during company research for URL: {CompanyUrl}", request.CompanyUrl);
            return Result.Failure<CompanyResearchResponse>(
                Error.Problem("CompanyResearchAgent.UnexpectedError", ex.Message));
        }
    }

    private static bool IsValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var uriResult) &&
               (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);
    }

    private static CompanyResearchResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            // Clean the response to handle markdown code blocks
            string cleanedResponse = CleanMarkdownCodeBlocks(aiResponse);

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonResponse = JsonSerializer.Deserialize<CompanyResearchJson>(cleanedResponse, jsonOptions);

            return new CompanyResearchResponse(
                jsonResponse?.CompanyName ?? "Unknown Company",
                jsonResponse?.CompanyDescription ?? "Company information not available",
                jsonResponse?.Industry ?? "Unknown",
                jsonResponse?.CompanySize ?? "Unknown",
                jsonResponse?.CompanyValues ?? "Not specified",
                jsonResponse?.RecentNews ?? "No recent news available",
                jsonResponse?.KeyProducts ?? "Not specified",
                jsonResponse?.CompanyMission ?? "Not specified",
                jsonResponse?.Confidence ?? 0.3,
                DateTime.UtcNow,
                AIModelType.Gemini); // TODO: Get actual model type from context

        }
        catch
        {
            // Fallback if JSON parsing fails
            return new CompanyResearchResponse(
                "Unknown Company",
                "Unable to research company information",
                "Unknown",
                "Unknown",
                "Not available",
                "Not available",
                "Not available",
                "Not available",
                0.2,
                DateTime.UtcNow,
                AIModelType.Gemini);
        }
    }

    private static string CleanMarkdownCodeBlocks(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return content;

        // Remove markdown code block markers
        content = content.Trim();
        if (content.StartsWith("```json"))
        {
            content = content[7..]; // Remove ```json
        }
        else if (content.StartsWith("```"))
        {
            content = content[3..]; // Remove ```
        }

        if (content.EndsWith("```"))
        {
            content = content[..^3]; // Remove trailing ```
        }

        return content.Trim();
    }

    private sealed class CompanyResearchJson
    {
        public string? CompanyName { get; set; }
        public string? CompanyDescription { get; set; }
        public string? Industry { get; set; }
        public string? CompanySize { get; set; }
        public string? CompanyValues { get; set; }
        public string? RecentNews { get; set; }
        public string? KeyProducts { get; set; }
        public string? CompanyMission { get; set; }
        public double? Confidence { get; set; }
    }
}
