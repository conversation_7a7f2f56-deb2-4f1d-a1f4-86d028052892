using Application.Abstractions.AI;
using Microsoft.Extensions.Logging;

namespace Infrastructure.AI;

public interface IFollowUpEmailPromptService : IBasePromptService<FollowUpEmailPromptConfiguration>
{
    string GetUserPrompt(string jobTitle, string companyUrl, DateTime applicationDate, FollowUpEmailType emailType);
    string GetInstructions(FollowUpEmailType emailType);
}

internal sealed class FollowUpEmailPromptService : BasePromptService<FollowUpEmailPromptConfiguration>, IFollowUpEmailPromptService
{
    public FollowUpEmailPromptService(ILogger<FollowUpEmailPromptService> logger) : base(logger)
    {
    }

    protected override string ResourceName => "Infrastructure.AI.Prompts.follow-up-email-prompts.yaml";

    public string GetSystemMessage()
    {
        return Configuration.Prompts.FollowUpEmail.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string companyUrl, DateTime applicationDate, FollowUpEmailType emailType)
    {
        var template = Configuration.Prompts.FollowUpEmail.UserPromptTemplate;
        var instructions = GetInstructions(emailType);
        var responseFormat = GetResponseFormat();

        var replacements = new Dictionary<string, string>
        {
            ["job_title"] = jobTitle,
            ["company_url"] = companyUrl,
            ["application_date"] = applicationDate.ToString("yyyy-MM-dd"),
            ["email_type"] = emailType.ToString(),
            ["company_research"] = "",
            ["instructions"] = instructions,
            ["response_format"] = responseFormat
        };

        return ReplaceTemplatePlaceholders(template, replacements);
    }

    public string GetInstructions(FollowUpEmailType emailType)
    {
        var baseInstructions = Configuration.Prompts.FollowUpEmail.Instructions;
        var emailTypeKey = emailType.ToString().ToLowerInvariant();

        return GetCategoryInstructions(
            baseInstructions,
            emailTypeKey,
            Configuration.EmailTypes,
            config => config.AdditionalInstructions);
    }

    public string GetResponseFormat()
    {
        return Configuration.Prompts.FollowUpEmail.ResponseFormat;
    }

    public FollowUpEmailPromptConfiguration GetConfiguration()
    {
        return Configuration;
    }

    protected override FollowUpEmailPromptConfiguration CreateDefaultConfiguration()
    {
        return new FollowUpEmailPromptConfiguration
        {
            Prompts = new FollowUpEmailPromptsSection
            {
                FollowUpEmail = new FollowUpEmailPrompt
                {
                    SystemMessage = "You are an expert career coach and professional communication specialist. Generate effective follow-up emails.",
                    UserPromptTemplate = "Generate a follow-up email for {job_title} at {company_url}. Applied on: {application_date}. Type: {email_type}. {instructions} {response_format}",
                    Instructions = "1. Create professional follow-up email\n2. Show continued interest\n3. Respect professional boundaries",
                    ResponseFormat = "Respond with JSON containing emailSubject, emailContent, summary, emailType, and confidence."
                }
            },
            EmailTypes = new Dictionary<string, EmailTypeConfig>(),
            QualityControl = new FollowUpEmailQualityControlConfig
            {
                MinConfidenceThreshold = 0.5,
                MaxContentLength = 2000,
                MinContentLength = 200,
                MaxSubjectLength = 60,
                RequiredElements = new[] { "professional greeting", "purpose statement", "main content", "professional closing" },
                ValidationRules = new[] { "Professional tone", "Concise content", "Clear call-to-action" }
            }
        };
    }
}

// Configuration classes
public sealed class FollowUpEmailPromptConfiguration
{
    public FollowUpEmailPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, EmailTypeConfig> EmailTypes { get; set; } = new();
    public Dictionary<string, IndustryConfig> Industries { get; set; } = new();
    public FollowUpEmailQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class FollowUpEmailPromptsSection
{
    public FollowUpEmailPrompt FollowUpEmail { get; set; } = new();
}

public sealed class FollowUpEmailPrompt : BasePromptConfig
{
}

public sealed class EmailTypeConfig
{
    public string Timing { get; set; } = string.Empty;
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class IndustryConfig
{
    public string Tone { get; set; } = string.Empty;
    public string Focus { get; set; } = string.Empty;
}

public sealed class FollowUpEmailQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public int MinContentLength { get; set; }
    public int MaxSubjectLength { get; set; }
    public string[] RequiredElements { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
