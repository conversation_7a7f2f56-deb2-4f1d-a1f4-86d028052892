﻿using System.Text;
using Application.Abstractions.AI;
using Application.Abstractions.Authentication;
using Application.Abstractions.BackgroundJobs;
using Application.Abstractions.Data;
using Application.Services;
using Hangfire;
using Hangfire.PostgreSql;
using Infrastructure.AI;
using Infrastructure.Authentication;
using Infrastructure.Authorization;
using Infrastructure.BackgroundJobs;
using Infrastructure.Database;
using Infrastructure.DomainEvents;
using Infrastructure.Interceptors;
using Infrastructure.Time;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using SharedKernel;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration) =>
        services
            .AddServices()
            .AddDatabase(configuration)
            .AddHealthChecks(configuration)
            .AddAuthenticationInternal(configuration)
            .AddAuthorizationInternal()
            .AddAIServices(configuration)
            .AddBackgroundJobs(configuration);

    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        services.AddSingleton<IDateTimeProvider, DateTimeProvider>();

        services.AddTransient<IDomainEventsDispatcher, DomainEventsDispatcher>();

        services.AddScoped<IHtmlContentProcessor, HtmlContentProcessor>();

        return services;
    }

    private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        string? connectionString = configuration.GetConnectionString("Database");

        services.AddScoped<AuditInterceptor>();

        services.AddDbContext<ApplicationDbContext>(
            (serviceProvider, options) => options
                .UseNpgsql(connectionString, npgsqlOptions =>
                    npgsqlOptions.MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Default))
                .UseSnakeCaseNamingConvention()
                .AddInterceptors(serviceProvider.GetRequiredService<AuditInterceptor>()));

        services.AddScoped<IApplicationDbContext>(sp => sp.GetRequiredService<ApplicationDbContext>());

        return services;
    }

    private static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services
            .AddHealthChecks()
            .AddNpgSql(configuration.GetConnectionString("Database")!);

        return services;
    }

    private static IServiceCollection AddAuthenticationInternal(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(o =>
            {
                o.RequireHttpsMetadata = false;
                o.TokenValidationParameters = new TokenValidationParameters
                {
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:Secret"]!)),
                    ValidIssuer = configuration["Jwt:Issuer"],
                    ValidAudience = configuration["Jwt:Audience"],
                    ClockSkew = TimeSpan.Zero
                };
            });

        services.AddHttpContextAccessor();
        services.AddScoped<IUserContext, UserContext>();
        services.AddSingleton<IPasswordHasher, PasswordHasher>();
        services.AddSingleton<ITokenProvider, TokenProvider>();

        return services;
    }

    private static IServiceCollection AddAuthorizationInternal(this IServiceCollection services)
    {
        services.AddAuthorization();

        services.AddScoped<PermissionProvider>();

        services.AddTransient<IAuthorizationHandler, PermissionAuthorizationHandler>();

        services.AddTransient<IAuthorizationPolicyProvider, PermissionAuthorizationPolicyProvider>();

        return services;
    }

    private static IServiceCollection AddAIServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure AI service options for backward compatibility
        services.Configure<AIServiceOptions>(configuration.GetSection(AIServiceOptions.GeminiSectionName));
        services.Configure<AIServiceOptions>(configuration.GetSection(AIServiceOptions.OpenAISectionName));

        // Configure new AI models options
        services.Configure<AIModelsOptions>(configuration.GetSection(AIModelsOptions.SectionName));

        // Configure web search options
        services.Configure<WebSearchOptions>(configuration.GetSection(WebSearchOptions.SectionName));

        // Register prompt services
        services.AddSingleton<IPromptService, PromptService>();
        services.AddSingleton<ICompanyResearchPromptService, CompanyResearchPromptService>();
        services.AddSingleton<ICoverLetterPromptService, CoverLetterPromptService>();
        services.AddSingleton<IFollowUpEmailPromptService, FollowUpEmailPromptService>();

        // Register web search service
        services.AddScoped<IWebSearchService, WebSearchService>();

        // Configure Semantic Kernel
        services.AddKernel();

        // Register AI model factory
        services.AddScoped<IAIModelFactory, AIModelFactory>();
        services.AddScoped<IAIChatServiceFactory, AIModelFactory>();

        // Register legacy AI service (for backward compatibility)
        services.AddScoped<IAIResumeCustomizationService, AIResumeCustomizationService>();

        // Register multi-agent system services
        services.AddScoped<IAgentFactory, AgentFactory>();
        services.AddScoped<IAgentOrchestrator, AgentOrchestrator>();

        // Register individual agents
        services.AddScoped<IResumeCustomizationAgent, ResumeCustomizationAgent>();
        services.AddScoped<ICompanyResearchAgent, CompanyResearchAgent>();
        services.AddScoped<ICoverLetterAgent, CoverLetterAgent>();
        services.AddScoped<IFollowUpEmailAgent, FollowUpEmailAgent>();

        return services;
    }

    private static IServiceCollection AddBackgroundJobs(this IServiceCollection services, IConfiguration configuration)
    {
     

        // Configure Hangfire for background job processing
        var connectionString = configuration.GetConnectionString("Database");

        services.AddHangfire(config =>
            config.UsePostgreSqlStorage(
                options => options.UseNpgsqlConnection(connectionString)));

        services.AddHangfireServer(options => options.SchedulePollingInterval = TimeSpan.FromSeconds(1));

        // Register Hangfire services manually
        services.AddSingleton<IBackgroundJobClient, BackgroundJobClient>();

        // Register background job classes
        services.AddScoped<AIResumeCustomizationJob>();
        services.AddScoped<MultiAgentWorkflowJob>();
        services.AddScoped<IBackgroundJobService, BackgroundJobService>();

        return services;
    }
}
