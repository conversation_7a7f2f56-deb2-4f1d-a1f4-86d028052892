using FluentValidation;

namespace Application.AI.ExecuteCompanyResearch;

internal sealed class ExecuteCompanyResearchCommandValidator : AbstractValidator<ExecuteCompanyResearchCommand>
{
    public ExecuteCompanyResearchCommandValidator()
    {
        RuleFor(x => x.JobApplicationId)
            .NotEmpty()
            .WithMessage("Job application ID is required");

        RuleFor(x => x.CompanyUrl)
            .NotEmpty()
            .WithMessage("Company URL is required")
            .Must(BeValidUrl)
            .WithMessage("Company URL must be a valid HTTP or HTTPS URL");

        RuleFor(x => x.JobTitle)
            .NotEmpty()
            .WithMessage("Job title is required")
            .MaximumLength(200)
            .WithMessage("Job title cannot exceed 200 characters");

        RuleFor(x => x.PreferredAIModel)
            .Must(model => model == null || Enum.IsDefined(typeof(Domain.AI.AIModelType), model.Value))
            .WithMessage("Preferred AI model must be a valid model type when specified");
    }

    private static bool BeValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var uriResult) &&
               (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);
    }
}
