using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using SharedKernel;
using System.Text.Json;

namespace Infrastructure.AI;

/// <summary>
/// Agent responsible for customizing resumes based on job requirements
/// Enhanced version that integrates with the multi-agent system
/// </summary>
internal sealed class ResumeCustomizationAgent : IResumeCustomizationAgent
{
    private readonly IAIChatServiceFactory _chatServiceFactory;
    private readonly IPromptService _promptService;
    private readonly ILogger<ResumeCustomizationAgent> _logger;

    public AgentType AgentType => AgentType.ResumeCustomization;

    public ResumeCustomizationAgent(
        IAIChatServiceFactory chatServiceFactory,
        IPromptService promptService,
        ILogger<ResumeCustomizationAgent> logger)
    {
        _chatServiceFactory = chatServiceFactory;
        _promptService = promptService;
        _logger = logger;
    }

    public async Task<Result<ResumeCustomizationResponse>> ProcessAsync(
        ResumeCustomizationRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting resume customization for JobApplication {JobApplicationId} using AI model: {AIModel}",
            request.JobApplicationId, request.PreferredAIModel?.ToString() ?? "Default");

        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.OriginalResumeContent))
            {
                return Result.Failure<ResumeCustomizationResponse>(
                    Error.Problem("ResumeCustomizationAgent.InvalidContent", "Resume content cannot be empty"));
            }

            if (string.IsNullOrWhiteSpace(request.JobDescription))
            {
                return Result.Failure<ResumeCustomizationResponse>(
                    Error.Problem("ResumeCustomizationAgent.InvalidJobDescription", "Job description cannot be empty"));
            }

            // Get chat completion service
            var chatServiceResult = _chatServiceFactory.CreateChatCompletionService(request.PreferredAIModel);
            if (chatServiceResult.IsFailure)
            {
                return Result.Failure<ResumeCustomizationResponse>(chatServiceResult.Error);
            }

            var chatCompletionService = chatServiceResult.Value;

            // Build prompts with company research context if available
            var systemMessage = _promptService.GetSystemMessage();
            var userPrompt = BuildUserPrompt(request);

            _logger.LogDebug("Sending resume customization request to AI service");

            // Create chat history
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(systemMessage);
            chatHistory.AddUserMessage(userPrompt);

            // Create execution settings
            var executionSettings = new GeminiPromptExecutionSettings
            {
                ResponseMimeType = "application/json"
            };

            // Call AI service with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(4));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var response = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                cancellationToken: combinedCts.Token);

            if (response?.Content is null || string.IsNullOrEmpty(response.Content))
            {
                _logger.LogError("AI service returned empty response");
                return Result.Failure<ResumeCustomizationResponse>(
                    Error.Problem("ResumeCustomizationAgent.EmptyResponse", "AI service returned empty response"));
            }

            // Parse AI response
            var customizationResponse = ParseAIResponse(response.Content);

            // Validate response quality
            var qualityControl = _promptService.GetConfiguration().QualityControl;
            if (customizationResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                _logger.LogWarning("Resume customization confidence {Confidence} is below threshold {Threshold}",
                    customizationResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            if (customizationResponse.CustomizedResumeContent.Length > qualityControl.MaxContentLength)
            {
                _logger.LogWarning("Customized content length {Length} exceeds maximum {MaxLength}",
                    customizationResponse.CustomizedResumeContent.Length, qualityControl.MaxContentLength);

                return Result.Failure<ResumeCustomizationResponse>(
                    Error.Problem("ResumeCustomizationAgent.ContentTooLong", "Customized content exceeds maximum length"));
            }

            _logger.LogInformation("Resume customization completed successfully with confidence {Confidence}",
                customizationResponse.ConfidenceScore);

            return Result.Success(customizationResponse);
        }
        catch (OperationCanceledException ex)
        {
            _logger.LogWarning(ex, "Resume customization was cancelled or timed out");
            return Result.Failure<ResumeCustomizationResponse>(
                Error.Problem("ResumeCustomizationAgent.Timeout", "Resume customization timed out"));
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response as JSON");
            return Result.Failure<ResumeCustomizationResponse>(
                Error.Problem("ResumeCustomizationAgent.InvalidResponse", "Failed to parse AI response"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during resume customization");
            return Result.Failure<ResumeCustomizationResponse>(
                Error.Problem("ResumeCustomizationAgent.UnexpectedError", ex.Message));
        }
    }

    private string BuildUserPrompt(ResumeCustomizationRequest request)
    {
        var basePrompt = _promptService.GetUserPrompt(
            request.JobTitle,
            request.CompanyUrl,
            request.JobDescription,
            request.OriginalResumeContent);

        // Enhance prompt with company research if available
        if (request.CompanyResearch is not null)
        {
            var companyContext = $"""
                
                **Additional Company Context:**
                - Company: {request.CompanyResearch.CompanyName}
                - Industry: {request.CompanyResearch.Industry}
                - Company Size: {request.CompanyResearch.CompanySize}
                - Mission: {request.CompanyResearch.CompanyMission}
                - Values: {request.CompanyResearch.CompanyValues}
                - Key Products: {request.CompanyResearch.KeyProducts}
                
                Use this company information to better tailor the resume customization.
                """;

            basePrompt += companyContext;
        }

        return basePrompt;
    }

    private static ResumeCustomizationResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            // Clean the response to handle markdown code blocks
            string cleanedResponse = CleanMarkdownCodeBlocks(aiResponse);

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonResponse = JsonSerializer.Deserialize<AIResponseJson>(cleanedResponse, jsonOptions);

            return new ResumeCustomizationResponse(
                jsonResponse?.CustomizedContent ?? aiResponse,
                jsonResponse?.Summary ?? "AI customization completed",
                jsonResponse?.KeyChanges ?? Array.Empty<string>(),
                jsonResponse?.Confidence ?? 0.7,
                DateTime.UtcNow,
                AIModelType.Gemini); // TODO: Get actual model type from context

        }
        catch
        {
            // Fallback if JSON parsing fails
            return new ResumeCustomizationResponse(
                aiResponse,
                "AI customization completed (fallback parsing)",
                Array.Empty<string>(),
                0.6,
                DateTime.UtcNow,
                AIModelType.Gemini);
        }
    }

    private static string CleanMarkdownCodeBlocks(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return content;

        // Remove markdown code block markers
        content = content.Trim();
        if (content.StartsWith("```json"))
        {
            content = content[7..]; // Remove ```json
        }
        else if (content.StartsWith("```"))
        {
            content = content[3..]; // Remove ```
        }

        if (content.EndsWith("```"))
        {
            content = content[..^3]; // Remove trailing ```
        }

        return content.Trim();
    }

    private sealed class AIResponseJson
    {
        public string? CustomizedContent { get; set; }
        public string? Summary { get; set; }
        public string[]? KeyChanges { get; set; }
        public double? Confidence { get; set; }
    }
}
