{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Web.Api/1.0.0": {"dependencies": {"AspNetCore.HealthChecks.UI.Client": "9.0.0", "Aspire.ServiceDefaults": "1.0.0", "CommunityToolkit.Aspire.OllamaSharp": "9.5.0", "Hangfire": "1.8.20", "Hangfire.AspNetCore": "1.8.20", "Infrastructure": "1.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "SonarAnalyzer.CSharp": "10.12.0.118525", "Swashbuckle.AspNetCore": "9.0.1"}, "runtime": {"Web.Api.dll": {}}}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/HealthChecks.NpgSql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.UI.Client/9.0.0": {"dependencies": {"AspNetCore.HealthChecks.UI.Core": "9.0.0"}, "runtime": {"lib/net8.0/HealthChecks.UI.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.UI.Core/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6"}, "runtime": {"lib/net8.0/HealthChecks.UI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.OllamaSharp/9.5.0": {"dependencies": {"Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Http": "9.0.6", "OllamaSharp": "5.1.12"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.OllamaSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dapper/2.0.123": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.123.33578"}}}, "EFCore.NamingConventions/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/EFCore.NamingConventions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation/12.0.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "FluentValidation.DependencyInjectionExtensions/12.0.0": {"dependencies": {"FluentValidation": "12.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire/1.8.20": {"dependencies": {"Hangfire.AspNetCore": "1.8.20", "Hangfire.Core": "1.8.20", "Hangfire.SqlServer": "1.8.20"}}, "Hangfire.AspNetCore/1.8.20": {"dependencies": {"Hangfire.NetCore": "1.8.20"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.Core/1.8.20": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.NetCore/1.8.20": {"dependencies": {"Hangfire.Core": "1.8.20", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.PostgreSql/1.20.12": {"dependencies": {"Dapper": "2.0.123", "Hangfire.Core": "1.8.20", "Microsoft.CSharp": "4.7.0", "Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Hangfire.SqlServer/1.8.20": {"dependencies": {"Hangfire.Core": "1.8.20"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.6", "System.Threading.Channels": "9.0.6"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Design/9.0.6": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.6"}}, "Microsoft.Extensions.AI/9.6.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Text.Json": "9.0.6", "System.Threading.Channels": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.6.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.6.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.ObjectPool": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.6.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.DependencyModel/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.6.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.6": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Features/8.0.15": {}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Http/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Http.Diagnostics/9.6.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.6.0", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Microsoft.Extensions.Telemetry": "9.6.0", "System.IO.Pipelines": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.Http.Polly/9.0.6": {"dependencies": {"Microsoft.Extensions.Http": "9.0.6", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Http.Resilience/9.6.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Http.Diagnostics": "9.6.0", "Microsoft.Extensions.ObjectPool": "9.0.6", "Microsoft.Extensions.Resilience": "9.6.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.ObjectPool/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Resilience/9.6.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.6.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Microsoft.Extensions.Telemetry.Abstractions": "9.6.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.ServiceDiscovery/9.3.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Features": "8.0.15", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.3.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "9.3.1.0", "fileVersion": "9.300.125.30506"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Features": "8.0.15", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "9.3.1.0", "fileVersion": "9.300.125.30506"}}}, "Microsoft.Extensions.Telemetry/9.6.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.6.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.6.0", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.ObjectPool": "9.0.6", "Microsoft.Extensions.Telemetry.Abstractions": "9.6.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.6.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.ObjectPool": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.VectorData.Abstractions": "9.7.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Connectors.Google/1.60.0-alpha": {"dependencies": {"Microsoft.SemanticKernel.Abstractions": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Core/1.60.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.SemanticKernel.Abstractions": "1.60.0", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.2": {}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "9.0.4.0", "fileVersion": "9.0.4.0"}}}, "Npgsql.OpenTelemetry/9.0.3": {"dependencies": {"Npgsql": "9.0.3", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net6.0/Npgsql.OpenTelemetry.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "OllamaSharp/5.1.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net9.0/OllamaSharp.dll": {"assemblyVersion": "5.1.12.0", "fileVersion": "5.1.12.0"}}}, "OpenTelemetry/1.12.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api/1.12.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.6"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"dependencies": {"OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "OpenTelemetry": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "1.12.0.490", "fileVersion": "1.12.0.490"}}}, "OpenTelemetry.Instrumentation.EntityFrameworkCore/1.12.0-beta.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.EntityFrameworkCore.dll": {"assemblyVersion": "1.12.0.561", "fileVersion": "1.12.0.561"}}}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "1.12.0.493", "fileVersion": "1.12.0.493"}}}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "1.12.0.496", "fileVersion": "1.12.0.496"}}}, "Polly/8.5.0": {"dependencies": {"Polly.Core": "8.5.0"}, "runtime": {"lib/net6.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.0.4130"}}}, "Polly.Core/8.5.0": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.0.4130"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Polly.Core": "8.5.0"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "8.5.0"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.5.0", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Scrutor/6.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6"}, "runtime": {"lib/net8.0/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SonarAnalyzer.CSharp/10.12.0.118525": {}, "Swashbuckle.AspNetCore/9.0.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.1"}}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.1"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.1": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"runtime": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.IO.Pipelines/9.0.6": {"runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Numerics.Tensors/9.0.6": {"runtime": {"lib/net9.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Json/9.0.6": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.Channels/9.0.6": {"runtime": {"lib/net9.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.RateLimiting/8.0.0": {}, "YamlDotNet/16.2.1": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "Application/1.0.0": {"dependencies": {"Domain": "1.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Scrutor": "6.1.0", "SharedKernel": "1.0.0"}, "runtime": {"Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Aspire.ServiceDefaults/1.0.0": {"dependencies": {"Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.ServiceDiscovery": "9.3.1", "Npgsql.OpenTelemetry": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.EntityFrameworkCore": "1.12.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0"}, "runtime": {"Aspire.ServiceDefaults.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Domain/1.0.0": {"dependencies": {"SharedKernel": "1.0.0"}, "runtime": {"Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Infrastructure/1.0.0": {"dependencies": {"Application": "1.0.0", "AspNetCore.HealthChecks.NpgSql": "9.0.0", "EFCore.NamingConventions": "9.0.0", "Hangfire.AspNetCore": "1.8.20", "Hangfire.Core": "1.8.20", "Hangfire.PostgreSql": "1.20.12", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.SemanticKernel.Connectors.Google": "1.60.0-alpha", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0", "YamlDotNet": "16.2.1"}, "runtime": {"Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharedKernel/1.0.0": {"runtime": {"SharedKernel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Web.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-npc58/AD5zuVxERdhCl2Kb7WnL37mwX42SJcXIwvmEig0/dugOLg3SIwtfvvh3TnvTwR/sk5LYNkkPaBdks61A==", "path": "aspnetcore.healthchecks.npgsql/9.0.0", "hashPath": "aspnetcore.healthchecks.npgsql.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.UI.Client/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Ub3Wvvbz7CMuFNWgLEc9qqQibiMoovDML/WHrwr5J83RPgtI20giCR92s/ipLgu7IIuqw+W/y7WpIeHqAICxg==", "path": "aspnetcore.healthchecks.ui.client/9.0.0", "hashPath": "aspnetcore.healthchecks.ui.client.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.UI.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TVriy4hgYnhfqz6NAzv8qe62Q8wf82iKUL6WV9selqeFZTq1ILi39Sic6sFQegRysvAVcnxKP/vY8z9Fk8x6XQ==", "path": "aspnetcore.healthchecks.ui.core/9.0.0", "hashPath": "aspnetcore.healthchecks.ui.core.9.0.0.nupkg.sha512"}, "CommunityToolkit.Aspire.OllamaSharp/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6krNvHagatlG9LIPobFBAnE36v1HaeOpAg3Ua3OjwgXuz0SLTgFgiwW5DoMsHmSdMZLv9iQTWccOVZVOroYgw==", "path": "communitytoolkit.aspire.ollamasharp/9.5.0", "hashPath": "communitytoolkit.aspire.ollamasharp.9.5.0.nupkg.sha512"}, "Dapper/2.0.123": {"type": "package", "serviceable": true, "sha512": "sha512-RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "path": "dapper/2.0.123", "hashPath": "dapper.2.0.123.nupkg.sha512"}, "EFCore.NamingConventions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-heKIYzPdEWx+Ba4xuG6jfEssW9rEi7I0lX38eoN7wo7qgg9uw7nn8UEmDQfwGEYPzSDpetCVANnDr5tqt2Asjg==", "path": "efcore.namingconventions/9.0.0", "hashPath": "efcore.namingconventions.9.0.0.nupkg.sha512"}, "FluentValidation/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA==", "path": "fluentvalidation/12.0.0", "hashPath": "fluentvalidation.12.0.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-B28fBRL1UjhGsBC8fwV6YB<PERSON><PERSON>+SiU1FxdD7l7p5dGPgRlVI7UnM+Lgzmg+unZtV1Zxzpaw96UY2MYfMaAd8cg==", "path": "fluentvalidation.dependencyinjectionextensions/12.0.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.12.0.0.nupkg.sha512"}, "Hangfire/1.8.20": {"type": "package", "serviceable": true, "sha512": "sha512-Q6W4JGbcr1Tg0IGyEg+VLx5OrhEKCIPAYm86RaRd6dpnozi6Ns6e6+71tRHTLMiz5XESf5msnZA61eW+xwZA6Q==", "path": "hangfire/1.8.20", "hashPath": "hangfire.1.8.20.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.20": {"type": "package", "serviceable": true, "sha512": "sha512-OoVxOZanKlnpyAuCAYWKGztQHYw6GTQxQD/W3bfOfFsD+8fYp7FEDKbMEsrrMvAtF+gLiY1HNk9xYjUpNnSxGA==", "path": "hangfire.aspnetcore/1.8.20", "hashPath": "hangfire.aspnetcore.1.8.20.nupkg.sha512"}, "Hangfire.Core/1.8.20": {"type": "package", "serviceable": true, "sha512": "sha512-PSk0daUo3WCcnh89Bydj/xJ+M7GA+eR4nXXD5v/CIBOTCAx+oa3/DNjqLJPC9QHojsKXt0DO6u87aGxCQZ78Og==", "path": "hangfire.core/1.8.20", "hashPath": "hangfire.core.1.8.20.nupkg.sha512"}, "Hangfire.NetCore/1.8.20": {"type": "package", "serviceable": true, "sha512": "sha512-QCMoUaOokUsScJIyyo9SDVaAOpPIaIpBhGzN7M9GgZI9Kzetd7Y+hmFlQUTpROi7bcASIGoTOPnPoqdNA///Rw==", "path": "hangfire.netcore/1.8.20", "hashPath": "hangfire.netcore.1.8.20.nupkg.sha512"}, "Hangfire.PostgreSql/1.20.12": {"type": "package", "serviceable": true, "sha512": "sha512-KvozigeVgbYSinFmaj5qQWRaBG7ey/S73UP6VLIOPcP1UrZO0/6hSw4jN53TKpWP2UsiMuYONRmQbFDxGAi4ug==", "path": "hangfire.postgresql/1.20.12", "hashPath": "hangfire.postgresql.1.20.12.nupkg.sha512"}, "Hangfire.SqlServer/1.8.20": {"type": "package", "serviceable": true, "sha512": "sha512-O+0A2dEf+cbKnJQRxK5jF5c4aLDCOf2+KllYBwjdO82KRVKz0nox8wirWQP/AKW85f4NJ93ZKAEtAWOFk6DLxw==", "path": "hangfire.sqlserver/1.8.20", "hashPath": "hangfire.sqlserver.1.8.20.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nH1mrzr77pk+n1E5+A/0KlzkNhqy3LS3gUGEjJf0PQE6PZAc3pr8rLwUATcaJMr/12qsxHT+kcvRZMxc4bxFpA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "path": "microsoft.aspnetcore.openapi/9.0.6", "hashPath": "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-6xabdZH2hOqSocjDIOd0FZLslH7kDX8ODY4lBR298GwkAkxuItjNgZHuRbTi9hmfDS2Hh02r+d17Fa8XT4lKLQ==", "path": "microsoft.entityframeworkcore.design/9.0.6", "hashPath": "microsoft.entityframeworkcore.design.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Qs+OnYPOrfb5wpSXNGdm9v3QattLhou26xamaICIE9jqWAW7xdzDlY/yXRz6zKFLnzRH70IM+XXYVElEVeQ9Q==", "path": "microsoft.entityframeworkcore.tools/9.0.6", "hashPath": "microsoft.entityframeworkcore.tools.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.AI/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-JrMdI7lKN23axyQpWLF2B1Pgzxo3+oO/1XNC90rlInlkdHnhOwqZ9vHlcZu5gZLtQPQLf6MbnWwgInm+GVuEpA==", "path": "microsoft.extensions.ai/9.6.0", "hashPath": "microsoft.extensions.ai.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-xGO7rHg3qK8jRdriAxIrsH4voNemCf8GVmgdcPXI5gpZ6lZWqOEM4ZO8yfYxUmg7+URw2AY1h7Uc/H17g7X1Kw==", "path": "microsoft.extensions.ai.abstractions/9.6.0", "hashPath": "microsoft.extensions.ai.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-voKvEpXEsYtEhSiIVrYrZsMP7zEkBjquhqcvhxOCUen1i9TwdSwBmz7tN93IthTPA1nzXzWnz9huCZyegiYM8A==", "path": "microsoft.extensions.ambientmetadata.application/9.6.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDM3a95WerM8g6IcMiBXq1qRS9dqmEUpgnCk2DeMWpPkYtp1ia+CkXabOnK93JmhVlUmv8l9WMPsCSUm+WqkIA==", "path": "microsoft.extensions.apidescription.server/8.0.0", "hashPath": "microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-EsW9aUhkHYfb75wkx24BuusOQbh2BRTSh052Fki2APn3puH1q9owynut1jWMq0Rm/C4zhyw6LAd+F6PX8HUi4Q==", "path": "microsoft.extensions.compliance.abstractions/9.6.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-71KqPTemVxSAYf4iv4lYFrL684MLwcTciLOHfoaWzxHG0U7ASWy/cQG8mNGB5Wy59H7eKTeuiNjvKXTebxlKWA==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.6.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "path": "microsoft.extensions.dependencymodel/9.0.6", "hashPath": "microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "path": "microsoft.extensions.diagnostics/9.0.6", "hashPath": "microsoft.extensions.diagnostics.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-cquw9eHjO7sJ+t6hC++Zd+UjelvxfAnmmfwIq7KnGllcxBg24VEsmIq5gODxYhxXN4rWOvmnIwix0ze2p5GbgA==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.6.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vq59f1teeXK8yXLeweTn0ozyM8zBtNEsRoNN4bXlOIxbtpF72lFhCNuve6iNytKm9isFpD2sNys6KrhMCD+jxg==", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.6", "hashPath": "microsoft.extensions.diagnostics.healthchecks.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-NZ8M8H7zz8rDAX+Em9/4XM7+7FD1XDzykfMZ62BDRd9ohHG3udDt0BxObpeP420o/Az+hDmLFT+IwVbhLkxCew==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-QUBxyUDGAyHppX5lDgE5XogfQ9fFtm3o4Un8cb46OMinto5hgYfKj3zZTR68QHAqKIM1iOoO6qrh1UbHUZkDQw==", "path": "microsoft.extensions.features/8.0.15", "hashPath": "microsoft.extensions.features.8.0.15.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.6", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-YoCEkjHHeeKsOzaJaGKuwsi1Ijckkm/+bv5RXmsKA0/qW4veY0eh5lVtkOXxkqQbVRuK3sObhxRM0UeuF6yAgA==", "path": "microsoft.extensions.http/9.0.6", "hashPath": "microsoft.extensions.http.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-FEhMpnH7OANl7ux2wuByvRYqqdRQGC7l2RKOd5FDFXySeWhqJnYWEaQPMqgNk1v108N3fIFmIEnGTOBHDpVP+Q==", "path": "microsoft.extensions.http.diagnostics/9.6.0", "hashPath": "microsoft.extensions.http.diagnostics.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-eui0JinN+aOSMt6oeAGhSk+wAw5nJbeHt+JyOltn4+zjA7FhYDDLdsUW+Md3x18p+NxUdTwh7W/4EbB5RJ90Aw==", "path": "microsoft.extensions.http.polly/9.0.6", "hashPath": "microsoft.extensions.http.polly.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-Np2a8u0ttPzqSrfVlVNRavKNzrzrbLAEsd0gR0KX5jIVOp7SVlPQdAHBTBh8/Hd7Amni9STSBWE2hoxq2pu3XA==", "path": "microsoft.extensions.http.resilience/9.6.0", "hashPath": "microsoft.extensions.http.resilience.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "path": "microsoft.extensions.logging.configuration/9.0.6", "hashPath": "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N1lSxMYI6DboY/PqCQwb6Bg74Baip7wWkHeHzuL7+PNsBipDmpBukXwVyVEAHdOdYNtasTdcXDVtbtwenoYU1g==", "path": "microsoft.extensions.objectpool/9.0.6", "hashPath": "microsoft.extensions.objectpool.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-JhfQk0u4XYGD21fMUvAxmzzVM3CMN2Xy3yemutEBECoSP5ND/7jEG4daL0NODSPtq6rd9Pk7SumnBfxyV3+zxw==", "path": "microsoft.extensions.resilience/9.6.0", "hashPath": "microsoft.extensions.resilience.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-z9tKpMm4V5LIMABboRjU2w+hYme4EUigi/OaOBcJZfRLHON/e6rtlkShZ0Opl4ownuReyCbUFbH2sD2C57NWvA==", "path": "microsoft.extensions.servicediscovery/9.3.1", "hashPath": "microsoft.extensions.servicediscovery.9.3.1.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-qQFNxjqj3jgLsRUwQRs15PCpibaV3EWqnKZH+JJouDZzaCVlEpryWndJHzzy/FZ6upr5e7S+dJPTOa2CyKGuSg==", "path": "microsoft.extensions.servicediscovery.abstractions/9.3.1", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.3.1.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-4k56GlByl+4gxwMHDMJ/MglbmjPPddLgd21RHZlSfx4WWLqiES/GJ/sHVCrKVjdIQHdcR5MLWvplfuqgj4H+VQ==", "path": "microsoft.extensions.telemetry/9.6.0", "hashPath": "microsoft.extensions.telemetry.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-LKkpXv0KCFC7oPzkqwNMgBfBImd8I57e6W1mtnvw5KCwMZ/1iS5PsWQiSxp17J91crAyKv5KosRF6lNK2j9EBQ==", "path": "microsoft.extensions.telemetry.abstractions/9.6.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vth/omSCX2vR0JabzSRU/hdPhr0CvUVZlaS2lJPWHrEwvak8ntrQLDtLMtMiWKSvviGBe/WmjUW8gA3qqn9tjw==", "path": "microsoft.extensions.vectordata.abstractions/9.7.0", "hashPath": "microsoft.extensions.vectordata.abstractions.9.7.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-//jUQGgpWHf3Q9cNCsa259/j2FnSNQFrnW3fLgdEZ+aRC/C727j75GjwVlAhbsDd0K2+p+x3/bEM9jVHcNrKlw==", "path": "microsoft.semantickernel.abstractions/1.60.0", "hashPath": "microsoft.semantickernel.abstractions.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.Google/1.60.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-RYYAWAytc+gjp3iCCOkrDC27AP7ya5twUNGu1N1H2I/fkRm2MqvGR1y10StA9qYjsMZ7LDGJDLA7sCf+ryYi1g==", "path": "microsoft.semantickernel.connectors.google/1.60.0-alpha", "hashPath": "microsoft.semantickernel.connectors.google.1.60.0-alpha.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-dE3JkvQNKYN29mTg4Fu6iwf1Ao51jrbReudqEoQhGePcsDBsjQh7YVs6PM4zwUtZzdMeKfkEF4n39HQEz86oXw==", "path": "microsoft.semantickernel.core/1.60.0", "hashPath": "microsoft.semantickernel.core.1.60.0.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-kN58RveGig9YjWAoYI3flDWC/jWCU0Xzzmp3f49fbnPwZLsVJu9qMt+VSrIz7I3Gn6jkeY1l7cVJopiRDOq3CQ==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.2", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.21.2.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "Npgsql.OpenTelemetry/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-YVxm5ekM6UNbLto3ttNShOy13jffA0szhlEWSDMka7PzN+Srp2XaHmGbUb/2ulbtkXSk4CZruWwbjZxbMvw93Q==", "path": "npgsql.opentelemetry/9.0.3", "hashPath": "npgsql.opentelemetry.9.0.3.nupkg.sha512"}, "OllamaSharp/5.1.12": {"type": "package", "serviceable": true, "sha512": "sha512-r5uhsPYH1jfot36dr3lqpY3visSNNgsPa8p4crh/DMoWeiY0LBrJlTVoVCZXbjU6J6kzSoW8wzMumPEYqgn4gw==", "path": "ollamasharp/5.1.12", "hashPath": "ollamasharp.5.1.12.nupkg.sha512"}, "OpenTelemetry/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-aIEu2O3xFOdwIVH0AJsIHPIMH1YuX18nzu7BHyaDNQ6NWSk4Zyrs9Pp6y8SATuSbvdtmvue4mj/QZ3838srbwA==", "path": "opentelemetry/1.12.0", "hashPath": "opentelemetry.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "path": "opentelemetry.api/1.12.0", "hashPath": "opentelemetry.api.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "hashPath": "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-7LzQSPhz5pNaL4xZgT3wkZODA1NLrEq3bet8KDHgtaJ9q+VNP7wmiZky8gQfMkB4FXuI/pevT8ZurL4p5997WA==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.12.0", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-6/8O6rsJRwslg5/Fm3bscBelw4Yh9T9CN24p7cAsuEFkrmmeSO9gkYUCK02Qi+CmPM2KHYTLjKi0lJaCsDMWQA==", "path": "opentelemetry.extensions.hosting/1.12.0", "hashPath": "opentelemetry.extensions.hosting.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.EntityFrameworkCore/1.12.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-4D2PLiJWbBbQbauojkIflT11WGVXoRU+xgox1mvOkpfm7YXIfwTtROOlcdscS51sMh5fgwjGKJtLWpLKppe7dw==", "path": "opentelemetry.instrumentation.entityframeworkcore/1.12.0-beta.2", "hashPath": "opentelemetry.instrumentation.entityframeworkcore.1.12.0-beta.2.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "path": "opentelemetry.instrumentation.http/1.12.0", "hashPath": "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xmd0TAm2x+T3ztdf5BolIwLPh+Uy6osaBeIQtCXv611PN7h/Pnhsjg5lU2hkAWj7M7ns74U5wtVpS8DXmJ+94w==", "path": "opentelemetry.instrumentation.runtime/1.12.0", "hashPath": "opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512"}, "Polly/8.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-GBNZPy7i7OpkaIruWPRJ0+AWzdGDQDnKY91b7Ic2aAch4lKhPjUc5KSffpH9krIWe0MoyghqaRxwRC0Uwz2PkA==", "path": "polly/8.5.0", "hashPath": "polly.8.5.0.nupkg.sha512"}, "Polly.Core/8.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-VYYMZNitZ85UEhwOKkTQI63WEMvzUqwQc74I2mm8h/DBVAMcBBxqYPni4DmuRtbCwngmuONuK2yBJfWNRKzI+A==", "path": "polly.core/8.5.0", "hashPath": "polly.core.8.5.0.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "Scrutor/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-m4+0RdgnX+jeiaqteq9x5SwEtuCjWG0KTw1jBjCzn7V8mCanXKoeF8+59E0fcoRbAjdEq6YqHFCmxZ49Kvqp3g==", "path": "scrutor/6.1.0", "hashPath": "scrutor.6.1.0.nupkg.sha512"}, "SonarAnalyzer.CSharp/10.12.0.118525": {"type": "package", "serviceable": true, "sha512": "sha512-uP38bsYegQBk8WOM6LYIAht6hrA7tcJgep/WuifPJjhjtjysPUP/iM/c1+P2+llNIDmm1s8Xh86+WG3K71eycw==", "path": "sonaranalyzer.csharp/10.12.0.118525", "hashPath": "sonaranalyzer.csharp.10.12.0.118525.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lmaz0juKGq8VgeCOki98OUVdEH7rvgBST0QrlWXNOxBl7ujNWyqdqj2SMAdgpQfTtBlSQUTHRslRM+0j7El5tA==", "path": "swashbuckle.aspnetcore/9.0.1", "hashPath": "swashbuckle.aspnetcore.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4/9tBsbeCN+ewwqjEBPq3BszNE9tOvA1iDogwT7qW7L0Uh942IozhtF9VaICD70XyZIlKNiHjc2Vt5QE09P4nw==", "path": "swashbuckle.aspnetcore.swagger/9.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Za5w1NfLMF0Tt4GTgC491wfmuuMwNw8A5nwZuytpBd4UXYZxkwNJa3QnRYGbUQD9MjLzQCYnKc8rMrr5LglW6A==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UuU+RvZbJZmnciVzi8G0jb3c6xeA4O8pQGrxl30eCK3pRNvurzZgS6shEei27q9wgvp0/UrTUNUM/a1kSlTfzw==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.1.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "path": "system.diagnostics.diagnosticsource/9.0.6", "hashPath": "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0nlr0reXrRmkZNKifKqh2DgGhQgfkT7Qa3gQxIn/JI7/y3WDiTz67M+Sq3vFhUqcG8O5zVrpqHvIHeGPGUBsEw==", "path": "system.io.pipelines/9.0.6", "hashPath": "system.io.pipelines.9.0.6.nupkg.sha512"}, "System.Numerics.Tensors/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-NOLvZVal7jhuhmLFNuMQnCUclSAEvemJlwjyBxoa8CeK6Oj8326bM4AqB2dcH+8FGna3X3ZtP4PCLrIScyddtA==", "path": "system.numerics.tensors/9.0.6", "hashPath": "system.numerics.tensors.9.0.6.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}, "System.Threading.Channels/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2MaNJVkG2yJiXQbZrrcYoJ55ehV+aX0zqR6rWJkO/Qj7jTsArWthrQ7iWywUf/sE5ylJWX/iLH2kKfwSRdkWsA==", "path": "system.threading.channels/9.0.6", "hashPath": "system.threading.channels.9.0.6.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "YamlDotNet/16.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-im6zTVgesjcfTRfuMpnx51Rg2svWenp/3q5XBfcIzgj8PNIkkSD2xEl9HWcVi2SaJPP9XcXUdzed9gSDEuf1TA==", "path": "yamldotnet/16.2.1", "hashPath": "yamldotnet.16.2.1.nupkg.sha512"}, "Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.ServiceDefaults/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SharedKernel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}