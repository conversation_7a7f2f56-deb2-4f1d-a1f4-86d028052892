using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.AI;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;
using System.Text.Json;

namespace Application.AI.ExecuteAgentWorkflow;

internal sealed class ExecuteAgentWorkflowCommandHandler 
    : ICommandHandler<ExecuteAgentWorkflowCommand, ExecuteAgentWorkflowResponse>
{
    private readonly IAgentOrchestrator _orchestrator;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<ExecuteAgentWorkflowCommandHandler> _logger;

    public ExecuteAgentWorkflowCommandHandler(
        IAgentOrchestrator orchestrator,
        IApplicationDbContext dbContext,
        ILogger<ExecuteAgentWorkflowCommandHandler> logger)
    {
        _orchestrator = orchestrator;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result<ExecuteAgentWorkflowResponse>> Handle(
        ExecuteAgentWorkflowCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing agent workflow for JobApplication {JobApplicationId} with agents: {Agents}",
            request.JobApplicationId, string.Join(", ", request.RequestedAgents));

        try
        {
            // Validate job application exists
            var jobApplication = await _dbContext.JobApplications
                .Include(ja => ja.Job)
                .Include(ja => ja.Resume)
                .FirstOrDefaultAsync(ja => ja.Id == request.JobApplicationId, cancellationToken);

            if (jobApplication is null)
            {
                return Result.Failure<ExecuteAgentWorkflowResponse>(
                    Error.NotFound("ExecuteAgentWorkflow.JobApplicationNotFound",
                        $"Job application with ID {request.JobApplicationId} was not found"));
            }

            // Create orchestration request
            var orchestrationRequest = new AgentOrchestrationRequest(
                request.JobApplicationId,
                request.RequestedAgents,
                request.ExecuteInParallel,
                request.PreferredAIModel);

            // Execute workflow
            var orchestrationResult = await _orchestrator.ExecuteWorkflowAsync(orchestrationRequest, cancellationToken);

            if (orchestrationResult.IsFailure)
            {
                return Result.Failure<ExecuteAgentWorkflowResponse>(orchestrationResult.Error);
            }

            var orchestrationResponse = orchestrationResult.Value;

            // Update job application with results
            await UpdateJobApplicationWithResults(jobApplication, orchestrationResponse, cancellationToken);

            // Convert to response format
            var results = new Dictionary<AgentType, AgentExecutionResult>();
            foreach (var agentType in request.RequestedAgents)
            {
                var hasResult = orchestrationResponse.AgentResults.ContainsKey(agentType);
                var confidenceScore = orchestrationResponse.GetConfidenceScore(agentType);

                results[agentType] = new AgentExecutionResult(
                    agentType,
                    hasResult,
                    confidenceScore,
                    hasResult ? null : "Agent execution failed",
                    hasResult ? orchestrationResponse.AgentResults[agentType] : null);
            }

            var response = new ExecuteAgentWorkflowResponse(
                request.JobApplicationId,
                results,
                orchestrationResponse.TotalExecutionTime,
                orchestrationResponse.IsSuccess,
                orchestrationResponse.ErrorMessage);

            _logger.LogInformation("Agent workflow completed for JobApplication {JobApplicationId}. Success: {Success}",
                request.JobApplicationId, response.IsSuccess);

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error executing agent workflow for JobApplication {JobApplicationId}",
                request.JobApplicationId);

            return Result.Failure<ExecuteAgentWorkflowResponse>(
                Error.Problem("ExecuteAgentWorkflow.UnexpectedError", ex.Message));
        }
    }

    private async Task UpdateJobApplicationWithResults(
        JobApplication jobApplication,
        AgentOrchestrationResponse orchestrationResponse,
        CancellationToken cancellationToken)
    {
        // Update resume customization results
        if (orchestrationResponse.AgentResults.TryGetValue(AgentType.ResumeCustomization, out var resumeResult) &&
            resumeResult is ResumeCustomizationResponse resumeResponse)
        {
            jobApplication.UpdateAICustomization(
                resumeResponse.CustomizedResumeContent,
                resumeResponse.CustomizationSummary,
                string.Join(",", resumeResponse.CustomizedKeyChanges),
                resumeResponse.ConfidenceScore);
        }

        // Update company research results
        if (orchestrationResponse.AgentResults.TryGetValue(AgentType.CompanyResearch, out var companyResult) &&
            companyResult is CompanyResearchResponse companyResponse)
        {
            var companyData = JsonSerializer.Serialize(companyResponse);
            jobApplication.UpdateCompanyResearch(companyData, companyResponse.ConfidenceScore);
        }

        // Update cover letter results
        if (orchestrationResponse.AgentResults.TryGetValue(AgentType.CoverLetter, out var coverLetterResult) &&
            coverLetterResult is CoverLetterResponse coverLetterResponse)
        {
            jobApplication.UpdateCoverLetter(
                coverLetterResponse.CoverLetterContent,
                coverLetterResponse.Summary,
                string.Join(",", coverLetterResponse.KeyHighlights),
                coverLetterResponse.ConfidenceScore);
        }

        // Update follow-up email results
        if (orchestrationResponse.AgentResults.TryGetValue(AgentType.FollowUpEmail, out var emailResult) &&
            emailResult is FollowUpEmailResponse emailResponse)
        {
            jobApplication.UpdateFollowUpEmail(
                emailResponse.EmailSubject,
                emailResponse.EmailContent,
                emailResponse.Summary,
                emailResponse.EmailType.ToString(),
                emailResponse.ConfidenceScore);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
