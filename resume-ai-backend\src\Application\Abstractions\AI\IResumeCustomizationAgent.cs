using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Agent responsible for customizing resumes based on job requirements
/// Enhanced version that integrates with the multi-agent system
/// </summary>
public interface IResumeCustomizationAgent : IAgent<ResumeCustomizationRequest, ResumeCustomizationResponse>
{
}

/// <summary>
/// Request for resume customization
/// </summary>
public sealed record ResumeCustomizationRequest(
    Guid JobApplicationId,
    string JobTitle,
    string JobDescription,
    string CompanyUrl,
    string OriginalResumeContent,
    CompanyResearchResponse? CompanyResearch = null,
    AIModelType? PreferredAIModel = null) : AgentRequest(JobApplicationId, PreferredAIModel);

/// <summary>
/// Response from resume customization
/// </summary>
public sealed record ResumeCustomizationResponse(
    string CustomizedResumeContent,
    string CustomizationSummary,
    string[] CustomizedKeyChanges,
    double ConfidenceScore,
    DateTime ProcessedAt,
    AIModelType AIModelUsed) : AgentResponse(ConfidenceScore, ProcessedAt, AIModelUsed);
