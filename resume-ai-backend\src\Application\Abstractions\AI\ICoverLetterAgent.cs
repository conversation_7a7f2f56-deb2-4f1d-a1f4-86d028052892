using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Agent responsible for generating tailored cover letters
/// </summary>
public interface ICoverLetterAgent : IAgent<CoverLetterRequest, CoverLetterResponse>
{
}

/// <summary>
/// Request for cover letter generation
/// </summary>
public sealed record CoverLetterRequest(
    Guid JobApplicationId,
    string JobTitle,
    string JobDescription,
    string CompanyUrl,
    string ResumeContent,
    CompanyResearchResponse? CompanyResearch = null,
    AIModelType? PreferredAIModel = null) : AgentRequest(JobApplicationId, PreferredAIModel);

/// <summary>
/// Response from cover letter generation
/// </summary>
public sealed record CoverLetterResponse(
    string CoverLetterContent,
    string Summary,
    string[] KeyHighlights,
    double ConfidenceScore,
    DateTime ProcessedAt,
    AIModelType AIModelUsed) : AgentResponse(ConfidenceScore, ProcessedAt, AIModelUsed);
