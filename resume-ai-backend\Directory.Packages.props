<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!--.NET 9 packages. Most of them will work on .NET 8. If you need to, make the updates here individually.-->
  <ItemGroup>
    <!-- Static code analysis -->
    <PackageVersion Include="Aspire.Hosting.PostgreSQL" Version="9.3.1" />
    <PackageVersion Include="Hangfire" Version="1.8.20" />
    <PackageVersion Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageVersion Include="Hangfire.Core" Version="1.8.20" />
    <PackageVersion Include="Hangfire.PostgreSql" Version="1.20.12" />
    <PackageVersion Include="Microsoft.SemanticKernel.Connectors.Google" Version="1.60.0-alpha" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="10.12.0.118525" />
    <!-- Application -->
    <PackageVersion Include="Scrutor" Version="6.1.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    <!-- Infrastructure -->
    <PackageVersion Include="AspNetCore.HealthChecks.NpgSql" Version="9.0.0" />
    <PackageVersion Include="EFCore.NamingConventions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Polly" Version="8.5.0" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="YamlDotNet" Version="16.2.1" />
    <!-- Web.Api -->
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <!-- ArchitectureTests -->
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="NetArchTest.Rules" Version="1.3.2" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <!-- Unit Tests -->
    <PackageVersion Include="FluentAssertions" Version="7.0.0" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <!--Aspire AppHost-->
    <PackageVersion Include="Aspire.Hosting.AppHost" Version="9.3.1" />
    <PackageVersion Include="CommunityToolkit.Aspire.Hosting.Ollama" Version="9.4.0" />
    <PackageVersion Include="CommunityToolkit.Aspire.OllamaSharp" Version="9.5.0" />
    <PackageVersion Include="Microsoft.Extensions.AI" Version="9.6.0" />
    <!--Aspire ServiceDefaults-->
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.6.0" />
    <PackageVersion Include="Microsoft.Extensions.ServiceDiscovery" Version="9.3.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageVersion Include="Npgsql.OpenTelemetry" Version="9.0.3" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.12.0-beta.2" />
  </ItemGroup>
</Project>