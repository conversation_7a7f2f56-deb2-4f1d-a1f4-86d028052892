using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using SharedKernel;
using System.Text.Json;

namespace Infrastructure.AI;

/// <summary>
/// Agent responsible for generating tailored cover letters
/// </summary>
internal sealed class CoverLetterAgent : ICoverLetterAgent
{
    private readonly IAIChatServiceFactory _chatServiceFactory;
    private readonly ICoverLetterPromptService _promptService;
    private readonly ILogger<CoverLetterAgent> _logger;

    public AgentType AgentType => AgentType.CoverLetter;

    public CoverLetterAgent(
        IAIChatServiceFactory chatServiceFactory,
        ICoverLetterPromptService promptService,
        ILogger<CoverLetterAgent> logger)
    {
        _chatServiceFactory = chatServiceFactory;
        _promptService = promptService;
        _logger = logger;
    }

    public async Task<Result<CoverLetterResponse>> ProcessAsync(
        CoverLetterRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting cover letter generation for JobApplication {JobApplicationId}, Position: {JobTitle}",
            request.JobApplicationId, request.JobTitle);

        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.JobDescription))
            {
                return Result.Failure<CoverLetterResponse>(
                    Error.Problem("CoverLetterAgent.InvalidJobDescription", "Job description cannot be empty"));
            }

            if (string.IsNullOrWhiteSpace(request.ResumeContent))
            {
                return Result.Failure<CoverLetterResponse>(
                    Error.Problem("CoverLetterAgent.InvalidResumeContent", "Resume content cannot be empty"));
            }

            // Get chat completion service
            var chatServiceResult = _chatServiceFactory.CreateChatCompletionService(request.PreferredAIModel);
            if (chatServiceResult.IsFailure)
            {
                return Result.Failure<CoverLetterResponse>(chatServiceResult.Error);
            }

            var chatCompletionService = chatServiceResult.Value;

            // Build prompts with company research context if available
            var systemMessage = _promptService.GetSystemMessage();
            var userPrompt = BuildUserPrompt(request);

            _logger.LogDebug("Sending cover letter generation request to AI service");

            // Create chat history
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(systemMessage);
            chatHistory.AddUserMessage(userPrompt);

            // Create execution settings
            var executionSettings = new GeminiPromptExecutionSettings
            {
                ResponseMimeType = "application/json"
            };

            // Call AI service with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(3));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var response = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                cancellationToken: combinedCts.Token);

            if (response?.Content is null || string.IsNullOrEmpty(response.Content))
            {
                _logger.LogError("AI service returned empty response for cover letter generation");
                return Result.Failure<CoverLetterResponse>(
                    Error.Problem("CoverLetterAgent.EmptyResponse", "AI service returned empty response"));
            }

            // Parse AI response
            var coverLetterResponse = ParseAIResponse(response.Content);

            // Validate response quality
            var qualityControl = _promptService.GetConfiguration().QualityControl;
            if (coverLetterResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                _logger.LogWarning("Cover letter confidence {Confidence} is below threshold {Threshold}",
                    coverLetterResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            if (coverLetterResponse.CoverLetterContent.Length > qualityControl.MaxContentLength)
            {
                _logger.LogWarning("Cover letter content length {Length} exceeds maximum {MaxLength}",
                    coverLetterResponse.CoverLetterContent.Length, qualityControl.MaxContentLength);

                return Result.Failure<CoverLetterResponse>(
                    Error.Problem("CoverLetterAgent.ContentTooLong", "Cover letter content exceeds maximum length"));
            }

            if (coverLetterResponse.CoverLetterContent.Length < qualityControl.MinContentLength)
            {
                _logger.LogWarning("Cover letter content length {Length} is below minimum {MinLength}",
                    coverLetterResponse.CoverLetterContent.Length, qualityControl.MinContentLength);

                return Result.Failure<CoverLetterResponse>(
                    Error.Problem("CoverLetterAgent.ContentTooShort", "Cover letter content is too short"));
            }

            _logger.LogInformation("Cover letter generation completed successfully with confidence {Confidence}",
                coverLetterResponse.ConfidenceScore);

            return Result.Success(coverLetterResponse);
        }
        catch (OperationCanceledException ex)
        {
            _logger.LogWarning(ex, "Cover letter generation was cancelled or timed out");
            return Result.Failure<CoverLetterResponse>(
                Error.Problem("CoverLetterAgent.Timeout", "Cover letter generation timed out"));
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response as JSON for cover letter generation");
            return Result.Failure<CoverLetterResponse>(
                Error.Problem("CoverLetterAgent.InvalidResponse", "Failed to parse AI response"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during cover letter generation");
            return Result.Failure<CoverLetterResponse>(
                Error.Problem("CoverLetterAgent.UnexpectedError", ex.Message));
        }
    }

    private string BuildUserPrompt(CoverLetterRequest request)
    {
        var basePrompt = _promptService.GetUserPrompt(
            request.JobTitle,
            request.JobDescription,
            request.CompanyUrl,
            request.ResumeContent);

        // Enhance prompt with company research if available
        if (request.CompanyResearch is not null)
        {
            var companyContext = $"""
                
                **Company Research Results:**
                - Company: {request.CompanyResearch.CompanyName}
                - Industry: {request.CompanyResearch.Industry}
                - Company Size: {request.CompanyResearch.CompanySize}
                - Mission: {request.CompanyResearch.CompanyMission}
                - Values: {request.CompanyResearch.CompanyValues}
                - Key Products: {request.CompanyResearch.KeyProducts}
                - Recent News: {request.CompanyResearch.RecentNews}
                
                Use this company information to create a more personalized and targeted cover letter.
                """;

            basePrompt += companyContext;
        }

        return basePrompt;
    }

    private static CoverLetterResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            // Clean the response to handle markdown code blocks
            string cleanedResponse = CleanMarkdownCodeBlocks(aiResponse);

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonResponse = JsonSerializer.Deserialize<CoverLetterJson>(cleanedResponse, jsonOptions);

            return new CoverLetterResponse(
                jsonResponse?.CoverLetterContent ?? "Unable to generate cover letter content",
                jsonResponse?.Summary ?? "Cover letter generation completed",
                jsonResponse?.KeyHighlights ?? Array.Empty<string>(),
                jsonResponse?.Confidence ?? 0.3,
                DateTime.UtcNow,
                AIModelType.Gemini); // TODO: Get actual model type from context

        }
        catch
        {
            // Fallback if JSON parsing fails
            return new CoverLetterResponse(
                "Unable to generate cover letter content due to parsing error",
                "Cover letter generation failed (fallback parsing)",
                Array.Empty<string>(),
                0.2,
                DateTime.UtcNow,
                AIModelType.Gemini);
        }
    }

    private static string CleanMarkdownCodeBlocks(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return content;

        // Remove markdown code block markers
        content = content.Trim();
        if (content.StartsWith("```json"))
        {
            content = content[7..]; // Remove ```json
        }
        else if (content.StartsWith("```"))
        {
            content = content[3..]; // Remove ```
        }

        if (content.EndsWith("```"))
        {
            content = content[..^3]; // Remove trailing ```
        }

        return content.Trim();
    }

    private sealed class CoverLetterJson
    {
        public string? CoverLetterContent { get; set; }
        public string? Summary { get; set; }
        public string[]? KeyHighlights { get; set; }
        public double? Confidence { get; set; }
    }
}
