using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Base interface for all AI agents in the multi-agent system
/// </summary>
public interface IAgent<TRequest, TResponse>
{
    /// <summary>
    /// The type of agent
    /// </summary>
    AgentType AgentType { get; }
    
    /// <summary>
    /// Process a request using the agent's specialized AI capabilities
    /// </summary>
    /// <param name="request">The request to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The agent's response</returns>
    Task<Result<TResponse>> ProcessAsync(TRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// Base request for all agent operations
/// </summary>
public abstract record AgentRequest(
    Guid JobApplicationId,
    AIModelType? PreferredAIModel = null);

/// <summary>
/// Base response for all agent operations
/// </summary>
public abstract record AgentResponse(
    double ConfidenceScore,
    DateTime ProcessedAt,
    AIModelType AIModelUsed);

/// <summary>
/// Context shared between agents during orchestrated workflows
/// </summary>
public sealed record AgentContext(
    Guid JobApplicationId,
    Guid JobId,
    Guid UserId,
    string JobTitle,
    string JobDescription,
    string CompanyUrl,
    string OriginalResumeContent,
    AIModelType? PreferredAIModel = null)
{
    /// <summary>
    /// Results from previous agents in the workflow
    /// </summary>
    public Dictionary<AgentType, object> AgentResults { get; init; } = new();
    
    /// <summary>
    /// Add result from an agent
    /// </summary>
    public AgentContext WithAgentResult(AgentType agentType, object result)
    {
        var newResults = new Dictionary<AgentType, object>(AgentResults)
        {
            [agentType] = result
        };
        
        return this with { AgentResults = newResults };
    }
    
    /// <summary>
    /// Get result from a specific agent
    /// </summary>
    public T? GetAgentResult<T>(AgentType agentType) where T : class
    {
        return AgentResults.TryGetValue(agentType, out var result) ? result as T : null;
    }
}
