using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.GenerateFollowUpEmail;

internal sealed class GenerateFollowUpEmailCommandHandler 
    : ICommandHandler<GenerateFollowUpEmailCommand, FollowUpEmailResponse>
{
    private readonly IFollowUpEmailAgent _agent;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<GenerateFollowUpEmailCommandHandler> _logger;

    public GenerateFollowUpEmailCommandHandler(
        IFollowUpEmailAgent agent,
        IApplicationDbContext dbContext,
        ILogger<GenerateFollowUpEmailCommandHandler> logger)
    {
        _agent = agent;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result<FollowUpEmailResponse>> Handle(
        GenerateFollowUpEmailCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating follow-up email for JobApplication {JobApplicationId}, Type: {EmailType}",
            request.JobApplicationId, request.EmailType);

        try
        {
            // Validate job application exists
            var jobApplication = await _dbContext.JobApplications
                .FirstOrDefaultAsync(ja => ja.Id == request.JobApplicationId, cancellationToken);

            if (jobApplication is null)
            {
                return Result.Failure<FollowUpEmailResponse>(
                    Error.NotFound("GenerateFollowUpEmail.JobApplicationNotFound",
                        $"Job application with ID {request.JobApplicationId} was not found"));
            }

            // Create agent request
            var agentRequest = new FollowUpEmailRequest(
                request.JobApplicationId,
                request.JobTitle,
                request.CompanyUrl,
                request.ApplicationDate,
                request.EmailType,
                request.CompanyResearch,
                request.PreferredAIModel);

            // Execute agent
            var agentResult = await _agent.ProcessAsync(agentRequest, cancellationToken);

            if (agentResult.IsFailure)
            {
                _logger.LogError("Follow-up email generation failed for JobApplication {JobApplicationId}: {Error}",
                    request.JobApplicationId, agentResult.Error.Description);
                return Result.Failure<FollowUpEmailResponse>(agentResult.Error);
            }

            var response = agentResult.Value;

            // Update job application with results
            jobApplication.UpdateFollowUpEmail(
                response.EmailSubject,
                response.EmailContent,
                response.Summary,
                response.EmailType.ToString(),
                response.ConfidenceScore);

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Follow-up email generation completed successfully for JobApplication {JobApplicationId} with confidence {Confidence}",
                request.JobApplicationId, response.ConfidenceScore);

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error generating follow-up email for JobApplication {JobApplicationId}",
                request.JobApplicationId);

            return Result.Failure<FollowUpEmailResponse>(
                Error.Problem("GenerateFollowUpEmail.UnexpectedError", ex.Message));
        }
    }
}
