using Microsoft.Extensions.Logging;

namespace Infrastructure.AI;

public interface IPromptService : IBasePromptService<PromptConfiguration>
{
    string GetUserPrompt(string jobTitle, string companyUrl, string jobDescription, string originalResumeContent);
    string GetInstructions(string? jobCategory = null);
}

internal sealed class PromptService : BasePromptService<PromptConfiguration>, IPromptService
{
    public PromptService(ILogger<PromptService> logger) : base(logger)
    {
    }

    protected override string ResourceName => "Infrastructure.AI.Prompts.resume-customization-prompts.yaml";

    public string GetSystemMessage()
    {
        return Configuration.Prompts.ResumeCustomization.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string companyUrl, string jobDescription, string originalResumeContent)
    {
        var template = Configuration.Prompts.ResumeCustomization.UserPromptTemplate;
        var instructions = GetInstructions();
        var responseFormat = GetResponseFormat();

        var replacements = new Dictionary<string, string>
        {
            ["job_title"] = jobTitle,
            ["company_url"] = companyUrl,
            ["job_description"] = jobDescription,
            ["original_resume_content"] = originalResumeContent,
            ["instructions"] = instructions,
            ["response_format"] = responseFormat
        };

        return ReplaceTemplatePlaceholders(template, replacements);
    }

    public string GetInstructions(string? jobCategory = null)
    {
        var baseInstructions = Configuration.Prompts.ResumeCustomization.Instructions;

        return GetCategoryInstructions(
            baseInstructions,
            jobCategory?.ToUpperInvariant(),
            Configuration.JobCategories,
            config => config.AdditionalInstructions);
    }

    public string GetResponseFormat()
    {
        return Configuration.Prompts.ResumeCustomization.ResponseFormat;
    }

    public PromptConfiguration GetConfiguration()
    {
        return Configuration;
    }

    protected override PromptConfiguration CreateDefaultConfiguration()
    {
        return new PromptConfiguration
        {
            Prompts = new PromptsSection
            {
                ResumeCustomization = new ResumeCustomizationPrompt
                {
                    SystemMessage = "You are an expert resume writer. Customize resumes to match job requirements while maintaining authenticity.",
                    UserPromptTemplate = "Customize this resume for the job: {job_title} at {company_url}. Job description: {job_description}. Resume: {original_resume_content}",
                    Instructions = "1. Highlight relevant skills\n2. Maintain factual information\n3. Use job keywords appropriately",
                    ResponseFormat = "Respond with JSON containing customizedContent, summary, and confidence fields."
                }
            },
            JobCategories = new Dictionary<string, JobCategoryConfig>(),
            QualityControl = new QualityControlConfig
            {
                MinConfidenceThreshold = 0.6,
                MaxContentLength = 50000,
                RequiredSections = new[] { "experience", "skills" },
                ValidationRules = new[] { "Maintain HTML structure", "No fictional content" }
            }
        };
    }
}

// Configuration classes
public sealed class PromptConfiguration
{
    public PromptsSection Prompts { get; set; } = new();
    public Dictionary<string, JobCategoryConfig> JobCategories { get; set; } = new();
    public QualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class PromptsSection
{
    public ResumeCustomizationPrompt ResumeCustomization { get; set; } = new();
}

public sealed class ResumeCustomizationPrompt : BasePromptConfig
{
}

public sealed class JobCategoryConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class QualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public string[] RequiredSections { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
