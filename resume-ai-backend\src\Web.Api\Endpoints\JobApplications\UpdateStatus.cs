using Application.Abstractions.Messaging;
using Application.JobApplications.UpdateStatus;
using Domain.JobApplications;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.JobApplications;

internal sealed class UpdateStatus : IEndpoint
{
    public sealed record UpdateJobApplicationStatusRequest(JobApplicationStatus Status);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPatch("job-applications/{jobApplicationId:guid}/status", async (
            Guid jobApplicationId,
            UpdateJobApplicationStatusRequest request,
            ICommandHandler<UpdateJobApplicationStatusCommand> commandHandler,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateJobApplicationStatusCommand(jobApplicationId, request.Status);

            Result result = await commandHandler.Handle(command, cancellationToken);

            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags(Tags.JobApplications)
        .WithName("UpdateJobApplicationStatus")
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
