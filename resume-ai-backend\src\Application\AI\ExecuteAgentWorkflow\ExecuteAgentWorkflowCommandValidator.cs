using FluentValidation;

namespace Application.AI.ExecuteAgentWorkflow;

internal sealed class ExecuteAgentWorkflowCommandValidator : AbstractValidator<ExecuteAgentWorkflowCommand>
{
    public ExecuteAgentWorkflowCommandValidator()
    {
        RuleFor(x => x.JobApplicationId)
            .NotEmpty()
            .WithMessage("Job application ID is required");

        RuleFor(x => x.RequestedAgents)
            .NotEmpty()
            .WithMessage("At least one agent must be requested")
            .Must(agents => agents.All(agent => Enum.IsDefined(typeof(Domain.AI.AgentType), agent)))
            .WithMessage("All requested agents must be valid agent types");

        RuleFor(x => x.PreferredAIModel)
            .Must(model => model == null || Enum.IsDefined(typeof(Domain.AI.AIModelType), model.Value))
            .WithMessage("Preferred AI model must be a valid model type when specified");
    }
}
