using Microsoft.Extensions.Logging;
using System.Reflection;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace Infrastructure.AI;

/// <summary>
/// Base class for all prompt services that handles common YAML loading and caching logic
/// </summary>
public abstract class BasePromptService<TConfiguration> where TConfiguration : class, new()
{
    private readonly Lazy<TConfiguration> _configuration;
    protected readonly ILogger Logger;

    protected BasePromptService(ILogger logger)
    {
        Logger = logger;
        _configuration = new Lazy<TConfiguration>(LoadConfiguration);
    }

    /// <summary>
    /// Gets the loaded configuration
    /// </summary>
    protected TConfiguration Configuration => _configuration.Value;

    /// <summary>
    /// Gets the resource name for the YAML configuration file
    /// </summary>
    protected abstract string ResourceName { get; }

    /// <summary>
    /// Creates a default configuration when YAML loading fails
    /// </summary>
    protected abstract TConfiguration CreateDefaultConfiguration();

    /// <summary>
    /// Loads the configuration from embedded YAML resource
    /// </summary>
    private TConfiguration LoadConfiguration()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            
            using Stream? stream = assembly.GetManifestResourceStream(ResourceName);
            if (stream == null)
            {
                Logger.LogError("Could not find embedded resource: {ResourceName}", ResourceName);
                return CreateDefaultConfiguration();
            }

            using var reader = new StreamReader(stream);
            string yamlContent = reader.ReadToEnd();

            IDeserializer deserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();

            TConfiguration configuration = deserializer.Deserialize<TConfiguration>(yamlContent);
            
            Logger.LogInformation("Successfully loaded prompt configuration from {ResourceName}", ResourceName);
            return configuration;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to load prompt configuration from {ResourceName}, using defaults", ResourceName);
            return CreateDefaultConfiguration();
        }
    }

    /// <summary>
    /// Replaces placeholders in a template string with provided values
    /// </summary>
    protected static string ReplaceTemplatePlaceholders(string template, Dictionary<string, string> replacements)
    {
        string result = template;
        foreach (var (placeholder, value) in replacements)
        {
            result = result.Replace($"{{{placeholder}}}", value);
        }
        return result;
    }

    /// <summary>
    /// Gets additional instructions for a specific category/type
    /// </summary>
    protected static string GetCategoryInstructions<TCategory>(
        string baseInstructions,
        string? categoryKey,
        Dictionary<string, TCategory> categories,
        Func<TCategory, string> instructionsSelector)
    {
        if (string.IsNullOrEmpty(categoryKey) || !categories.TryGetValue(categoryKey.ToLowerInvariant(), out var categoryConfig))
        {
            return baseInstructions;
        }

        var additionalInstructions = instructionsSelector(categoryConfig);
        return string.IsNullOrEmpty(additionalInstructions) 
            ? baseInstructions 
            : $"{baseInstructions}\n\n**Additional Instructions:**\n{additionalInstructions}";
    }
}

/// <summary>
/// Interface for prompt services that provide basic prompt functionality
/// </summary>
public interface IBasePromptService<TConfiguration> where TConfiguration : class
{
    /// <summary>
    /// Gets the system message for the AI
    /// </summary>
    string GetSystemMessage();

    /// <summary>
    /// Gets the response format instructions
    /// </summary>
    string GetResponseFormat();

    /// <summary>
    /// Gets the full configuration
    /// </summary>
    TConfiguration GetConfiguration();
}

/// <summary>
/// Common quality control configuration used across all prompt services
/// </summary>
public sealed class BaseQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public string[] RequiredFields { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Base prompt configuration that contains common elements
/// </summary>
public abstract class BasePromptConfig
{
    public string SystemMessage { get; set; } = string.Empty;
    public string UserPromptTemplate { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
    public string ResponseFormat { get; set; } = string.Empty;
    public string FallbackInstructions { get; set; } = string.Empty;
}
