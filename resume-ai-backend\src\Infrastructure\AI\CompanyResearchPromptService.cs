using Microsoft.Extensions.Logging;

namespace Infrastructure.AI;

public interface ICompanyResearchPromptService : IBasePromptService<CompanyResearchPromptConfiguration>
{
    string GetUserPrompt(string companyUrl, string jobTitle);
    string GetUserPromptWithSearchContext(string companyUrl, string jobTitle, string searchContext);
    string GetInstructions(string? companyType = null);
}

internal sealed class CompanyResearchPromptService : BasePromptService<CompanyResearchPromptConfiguration>, ICompanyResearchPromptService
{
    public CompanyResearchPromptService(ILogger<CompanyResearchPromptService> logger) : base(logger)
    {
    }

    protected override string ResourceName => "Infrastructure.AI.Prompts.company-research-prompts.yaml";

    public string GetSystemMessage()
    {
        return Configuration.Prompts.CompanyResearch.SystemMessage;
    }

    public string GetUserPrompt(string companyUrl, string jobTitle)
    {
        var template = Configuration.Prompts.CompanyResearch.UserPromptTemplate;
        var instructions = GetInstructions();
        var responseFormat = GetResponseFormat();

        var replacements = new Dictionary<string, string>
        {
            ["company_url"] = companyUrl,
            ["job_title"] = jobTitle,
            ["instructions"] = instructions,
            ["response_format"] = responseFormat
        };

        return ReplaceTemplatePlaceholders(template, replacements);
    }

    public string GetUserPromptWithSearchContext(string companyUrl, string jobTitle, string searchContext)
    {
        var template = GetEnhancedTemplateWithSearchContext();
        var instructions = GetInstructionsWithSearchContext();
        var responseFormat = GetResponseFormat();

        var replacements = new Dictionary<string, string>
        {
            ["company_url"] = companyUrl,
            ["job_title"] = jobTitle,
            ["instructions"] = instructions,
            ["response_format"] = responseFormat,
            ["search_context"] = searchContext
        };

        return ReplaceTemplatePlaceholders(template, replacements);
    }

    public string GetInstructions(string? companyType = null)
    {
        var baseInstructions = Configuration.Prompts.CompanyResearch.Instructions;

        return GetCategoryInstructions(
            baseInstructions,
            companyType,
            Configuration.CompanyTypes,
            config => config.AdditionalInstructions);
    }

    public string GetResponseFormat()
    {
        return Configuration.Prompts.CompanyResearch.ResponseFormat;
    }

    public CompanyResearchPromptConfiguration GetConfiguration()
    {
        return Configuration;
    }

    private string GetEnhancedTemplateWithSearchContext()
    {
        return """
        Please research the following company and extract relevant information for a job applicant.

        **Company Details:**
        - Company URL: {company_url}
        - Job Position: {job_title}

        **Web Search Results:**
        {search_context}

        **Research Instructions:**
        {instructions}

        **Response Format:**
        {response_format}
        """;
    }

    private string GetInstructionsWithSearchContext()
    {
        var baseInstructions = Configuration.Prompts.CompanyResearch.Instructions;

        var enhancedInstructions = baseInstructions + """

        **Additional Instructions for Web Search Results:**
        1. Use the provided web search results as your primary source of information
        2. Cross-reference information from multiple search results when available
        3. Prioritize recent and official company information
        4. If search results are limited or unclear, indicate lower confidence
        5. Focus on information that is most relevant to the job position
        6. Verify consistency across different sources in the search results
        """;

        return enhancedInstructions;
    }

    protected override CompanyResearchPromptConfiguration CreateDefaultConfiguration()
    {
        return new CompanyResearchPromptConfiguration
        {
            Prompts = new CompanyResearchPromptsSection
            {
                CompanyResearch = new CompanyResearchPrompt
                {
                    SystemMessage = "You are an expert business researcher. Research companies and extract relevant information for job applicants.",
                    UserPromptTemplate = "Research the company at {company_url} for the position: {job_title}. {instructions} {response_format}",
                    Instructions = "1. Extract company information\n2. Focus on publicly available data\n3. Provide accurate information only",
                    ResponseFormat = "Respond with JSON containing company details and confidence score."
                }
            },
            CompanyTypes = new Dictionary<string, CompanyTypeConfig>(),
            QualityControl = new CompanyResearchQualityControlConfig
            {
                MinConfidenceThreshold = 0.4,
                MaxDescriptionLength = 500,
                MaxNewsLength = 300,
                RequiredFields = new[] { "companyName", "companyDescription", "industry" },
                ValidationRules = new[] { "Factual information only", "No assumptions" }
            }
        };
    }
}

// Configuration classes
public sealed class CompanyResearchPromptConfiguration
{
    public CompanyResearchPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, CompanyTypeConfig> CompanyTypes { get; set; } = new();
    public CompanyResearchQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class CompanyResearchPromptsSection
{
    public CompanyResearchPrompt CompanyResearch { get; set; } = new();
}

public sealed class CompanyResearchPrompt : BasePromptConfig
{
}

public sealed class CompanyTypeConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class CompanyResearchQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxDescriptionLength { get; set; }
    public int MaxNewsLength { get; set; }
    public string[] RequiredFields { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
