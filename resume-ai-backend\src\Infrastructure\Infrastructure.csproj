﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.NpgSql" />
    <PackageReference Include="EFCore.NamingConventions" />
    <PackageReference Include="Hangfire.Core" />
    <PackageReference Include="Hangfire.PostgreSql" />
    <PackageReference Include="Hangfire.AspNetCore" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.Extensions.AI" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Google" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions.Http" />
    <PackageReference Include="YamlDotNet" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
  </ItemGroup>

  <ItemGroup>
	<InternalsVisibleTo Include="ArchitectureTests" />
	<InternalsVisibleTo Include="Infrastructure.UnitTests" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Database\Migrations\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="AI\Prompts\resume-customization-prompts.yaml" >
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="AI\Prompts\follow-up-email-prompts.yaml" >
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="AI\Prompts\company-research-prompts.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="AI\Prompts\cover-letter-prompts.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

</Project>
