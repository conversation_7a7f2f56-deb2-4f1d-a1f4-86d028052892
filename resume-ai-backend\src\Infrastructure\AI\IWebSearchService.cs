using Microsoft.SemanticKernel.Data;
using SharedKernel;

namespace Infrastructure.AI;

/// <summary>
/// Service for performing web searches to gather company information
/// </summary>
internal interface IWebSearchService
{
    /// <summary>
    /// Searches for company information using web search
    /// </summary>
    /// <param name="companyUrl">The company URL to research</param>
    /// <param name="jobTitle">The job title for context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search results containing company information</returns>
    Task<Result<CompanySearchResults>> SearchCompanyInformationAsync(
        string companyUrl, 
        string jobTitle, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Results from company web search
/// </summary>
internal sealed record CompanySearchResults(
    string CompanyName,
    IReadOnlyList<SearchResult> SearchResults,
    double ConfidenceScore)
{
    /// <summary>
    /// Gets the combined search content for AI processing
    /// </summary>
    public string GetCombinedContent()
    {
        if (!SearchResults.Any())
            return "No search results available.";

        var content = new System.Text.StringBuilder();
        content.AppendLine($"Company: {CompanyName}");
        content.AppendLine();

        foreach (var result in SearchResults.Take(5)) // Limit to top 5 results
        {
            content.AppendLine($"Source: {result.Name}");
            content.AppendLine($"URL: {result.Link}");
            content.AppendLine($"Content: {result.Value}");
            content.AppendLine("---");
        }

        return content.ToString();
    }
}

/// <summary>
/// Individual search result
/// </summary>
internal sealed record SearchResult(
    string Name,
    string Value,
    string Link);
