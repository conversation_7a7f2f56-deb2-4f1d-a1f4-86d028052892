using Application.Abstractions.Messaging;
using Domain.AI;

namespace Application.AI.ExecuteAgentWorkflow;

public sealed record ExecuteAgentWorkflowCommand(
    Guid JobApplicationId,
    AgentType[] RequestedAgents,
    bool ExecuteInParallel = false,
    AIModelType? PreferredAIModel = null) : ICommand<ExecuteAgentWorkflowResponse>;

public sealed record ExecuteAgentWorkflowResponse(
    Guid JobApplicationId,
    Dictionary<AgentType, AgentExecutionResult> Results,
    TimeSpan TotalExecutionTime,
    bool IsSuccess,
    string? ErrorMessage = null);

public sealed record AgentExecutionResult(
    AgentType AgentType,
    bool IsSuccess,
    double ConfidenceScore,
    string? ErrorMessage = null,
    object? Data = null);
