# Web Search Integration for CompanyResearchAgent

## Overview

This document describes the implementation of web search capabilities in the CompanyResearchAgent using Microsoft Semantic Kernel's text search functionality. The integration follows the patterns shown in the Microsoft documentation and maintains consistency with the existing CQRS pattern and clean architecture principles.

## Architecture

### Components

1. **IWebSearchService** - Interface for web search operations
2. **WebSearchService** - Implementation using Google Custom Search API
3. **CompanyResearchAgent** - Enhanced with web search capabilities
4. **CompanyResearchPromptService** - Extended with search context support

### Key Features

- **Google Custom Search Integration**: Uses Google's Custom Search API through Semantic Kernel
- **Site-specific Search**: Prioritizes results from the company's own domain
- **Fallback Search**: Performs broader search if site-specific search yields no results
- **Confidence Scoring**: Calculates confidence based on search result quality
- **Error Handling**: Graceful degradation when search fails
- **Clean Architecture**: Follows existing patterns and dependency injection

## Implementation Details

### Web Search Service

The `WebSearchService` implements the following search strategy:

1. **Extract Company Name**: Derives company name from URL for better search queries
2. **Multiple Search Queries**: Creates targeted queries for different aspects:
   - Company information and about us
   - Mission, values, and culture
   - Company size and industry
   - Recent news and achievements
   - Products and services
   - Job-specific information

3. **Site-specific Search**: Uses Google's `siteSearch` filter to prioritize company domain results
4. **Broader Search**: Falls back to general search if site-specific yields no results
5. **Result Processing**: Combines and formats results for AI processing

### Enhanced Prompt Service

The `CompanyResearchPromptService` now includes:

- `GetUserPromptWithSearchContext()`: Creates prompts that include web search results
- Enhanced instructions for processing search results
- Template that incorporates search context into the research process

### Integration with CompanyResearchAgent

The agent now:

1. Performs web search before AI processing
2. Includes search results in the prompt context
3. Continues gracefully if search fails
4. Maintains existing error handling and logging patterns

## Configuration

### appsettings.json

Add the following configuration section:

```json
{
  "WebSearch": {
    "GoogleSearchEngineId": "your-google-search-engine-id",
    "GoogleApiKey": "your-google-api-key",
    "MaxResults": 10,
    "TimeoutSeconds": 30
  }
}
```

### Google Custom Search Setup

1. Create a Google Cloud Project
2. Enable the Custom Search API
3. Create a Custom Search Engine at https://cse.google.com/
4. Get your Search Engine ID and API Key
5. Configure the search engine to search the entire web or specific sites

## Usage

The web search functionality is automatically integrated into the existing CompanyResearchAgent workflow:

```csharp
var request = new CompanyResearchRequest(
    jobApplicationId: Guid.NewGuid(),
    companyUrl: "https://example.com",
    jobTitle: "Software Engineer"
);

var result = await companyResearchAgent.ProcessAsync(request);
```

The agent will:
1. Perform web search for the company
2. Use search results to enhance AI research
3. Return comprehensive company information

## Search Result Processing

### Search Queries Generated

For a company like "Acme Corp", the service generates queries such as:
- "Acme Corp company information about us"
- "Acme Corp company mission values culture"
- "Acme Corp company size industry business"
- "Acme Corp recent news achievements"
- "Acme Corp products services solutions"
- "Acme Corp Software Engineer job career opportunities"

### Confidence Scoring

The confidence score is calculated based on:
- Number of search results found (base score: 0.3)
- Results per query (up to +0.4)
- Company name mentions in results (up to +0.3)
- Maximum confidence: 1.0

### Result Formatting

Search results are formatted for AI processing:
```
Company: Acme Corp

Source: About Us - Acme Corp
URL: https://acme.com/about
Content: Acme Corp is a leading provider of innovative solutions...
---

Source: Careers - Acme Corp  
URL: https://acme.com/careers
Content: Join our team of talented professionals...
---
```

## Error Handling

The implementation includes comprehensive error handling:

1. **Search Failures**: Continues without search context if web search fails
2. **API Limits**: Graceful handling of API rate limits and quotas
3. **Network Issues**: Timeout handling and retry logic
4. **Invalid URLs**: Validation and fallback for malformed company URLs
5. **Empty Results**: Fallback to broader search strategies

## Performance Considerations

- **Parallel Queries**: Multiple search queries are executed sequentially to avoid rate limits
- **Result Limiting**: Limits results per query (default: 3) and total results (default: 5)
- **Timeout Management**: 30-second timeout for search operations
- **Resource Disposal**: Proper disposal of search service resources

## Security and Privacy

- **API Key Protection**: API keys stored in configuration, not in code
- **Rate Limiting**: Respects Google API rate limits
- **Data Privacy**: No storage of search results beyond request processing
- **Error Logging**: Sensitive information excluded from logs

## Testing

The implementation can be tested by:

1. **Unit Tests**: Mock the IWebSearchService for isolated testing
2. **Integration Tests**: Test with actual Google Custom Search API
3. **End-to-End Tests**: Full workflow testing with real company URLs

## Monitoring and Logging

The service includes comprehensive logging:
- Search query execution
- Result counts and confidence scores
- Error conditions and fallbacks
- Performance metrics

## Future Enhancements

Potential improvements include:
- **Bing Search Support**: Alternative search provider
- **Caching**: Cache search results to reduce API calls
- **Advanced Filtering**: More sophisticated result filtering
- **Multi-language Support**: Search in different languages
- **Semantic Search**: Use vector search for better relevance
