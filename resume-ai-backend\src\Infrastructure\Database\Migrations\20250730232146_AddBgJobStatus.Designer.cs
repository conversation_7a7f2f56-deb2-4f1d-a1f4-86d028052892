﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Infrastructure.Database.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250730232146_AddBgJobStatus")]
    partial class AddBgJobStatus
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Domain.History.History", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrentValue")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("current_value");

                    b.Property<string>("PreviousValue")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("previous_value");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("property_name");

                    b.Property<string>("PropertyType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("property_type");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("table_name");

                    b.HasKey("Id")
                        .HasName("pk_histories");

                    b.HasIndex("TableName", "CreatedAt")
                        .HasDatabaseName("ix_histories_table_name_created_at");

                    b.ToTable("histories", "public");
                });

            modelBuilder.Entity("Domain.JobApplications.JobApplication", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<double?>("AIConfidenceScore")
                        .HasPrecision(3, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("ai_confidence_score");

                    b.Property<string>("AICustomizationSummary")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ai_customization_summary");

                    b.Property<string>("AICustomizedContent")
                        .HasColumnType("text")
                        .HasColumnName("ai_customized_content");

                    b.Property<string>("AICustomizedKeyChanges")
                        .HasColumnType("text")
                        .HasColumnName("ai_customized_key_changes");

                    b.Property<DateTime?>("AIProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ai_processed_at");

                    b.Property<double?>("CompanyResearchConfidence")
                        .HasPrecision(3, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("company_research_confidence");

                    b.Property<string>("CompanyResearchData")
                        .HasColumnType("text")
                        .HasColumnName("company_research_data");

                    b.Property<DateTime?>("CompanyResearchProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("company_research_processed_at");

                    b.Property<double?>("CoverLetterConfidence")
                        .HasPrecision(3, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("cover_letter_confidence");

                    b.Property<string>("CoverLetterContent")
                        .HasColumnType("text")
                        .HasColumnName("cover_letter_content");

                    b.Property<string>("CoverLetterKeyHighlights")
                        .HasColumnType("text")
                        .HasColumnName("cover_letter_key_highlights");

                    b.Property<DateTime?>("CoverLetterProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("cover_letter_processed_at");

                    b.Property<string>("CoverLetterSummary")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("cover_letter_summary");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<double?>("FollowUpEmailConfidence")
                        .HasPrecision(3, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("follow_up_email_confidence");

                    b.Property<string>("FollowUpEmailContent")
                        .HasColumnType("text")
                        .HasColumnName("follow_up_email_content");

                    b.Property<DateTime?>("FollowUpEmailProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("follow_up_email_processed_at");

                    b.Property<string>("FollowUpEmailSubject")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("follow_up_email_subject");

                    b.Property<string>("FollowUpEmailSummary")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("follow_up_email_summary");

                    b.Property<string>("FollowUpEmailType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("follow_up_email_type");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("JobId")
                        .HasColumnType("uuid")
                        .HasColumnName("job_id");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<Guid>("ResumeId")
                        .HasColumnType("uuid")
                        .HasColumnName("resume_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_job_applications");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_job_applications_created_by");

                    b.HasIndex("JobId")
                        .HasDatabaseName("ix_job_applications_job_id");

                    b.HasIndex("ResumeId", "JobId")
                        .IsUnique()
                        .HasDatabaseName("ix_job_applications_resume_id_job_id");

                    b.ToTable("job_applications", "public");
                });

            modelBuilder.Entity("Domain.Jobs.Job", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("AppliedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("applied_at");

                    b.Property<string>("BackgroundJobId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("background_job_id");

                    b.Property<string>("CompanyUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("company_url");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("JobDescription")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_description");

                    b.Property<string>("JobPostingUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_posting_url");

                    b.Property<string>("JobTitle")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_title");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<int?>("PreferredAIModel")
                        .HasColumnType("integer")
                        .HasColumnName("preferred_ai_model");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_jobs");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_jobs_user_id");

                    b.ToTable("jobs", "public");
                });

            modelBuilder.Entity("Domain.Resumes.Resume", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_id");

                    b.Property<string>("ResumeContent")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("resume_content");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_resumes");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_resumes_parent_id");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_resumes_user_id_parent_unique")
                        .HasFilter("parent_id IS NULL");

                    b.ToTable("resumes", "public");
                });

            modelBuilder.Entity("Domain.Todos.TodoItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_at");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_completed");

                    b.PrimitiveCollection<List<string>>("Labels")
                        .IsRequired()
                        .HasColumnType("text[]")
                        .HasColumnName("labels");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_todo_items");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_todo_items_user_id");

                    b.ToTable("todo_items", "public");
                });

            modelBuilder.Entity("Domain.Users.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("first_name");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("last_name");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_users_email");

                    b.ToTable("users", "public");
                });

            modelBuilder.Entity("Domain.JobApplications.JobApplication", b =>
                {
                    b.HasOne("Domain.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_job_applications_users_created_by");

                    b.HasOne("Domain.Jobs.Job", "Job")
                        .WithMany()
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_job_applications_jobs_job_id");

                    b.HasOne("Domain.Resumes.Resume", "Resume")
                        .WithMany()
                        .HasForeignKey("ResumeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_job_applications_resumes_resume_id");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Job");

                    b.Navigation("Resume");
                });

            modelBuilder.Entity("Domain.Jobs.Job", b =>
                {
                    b.HasOne("Domain.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_jobs_users_user_id");
                });

            modelBuilder.Entity("Domain.Resumes.Resume", b =>
                {
                    b.HasOne("Domain.Resumes.Resume", null)
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_resumes_resumes_parent_id");

                    b.HasOne("Domain.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_resumes_users_user_id");
                });

            modelBuilder.Entity("Domain.Todos.TodoItem", b =>
                {
                    b.HasOne("Domain.Users.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_todo_items_users_user_id");
                });
#pragma warning restore 612, 618
        }
    }
}
