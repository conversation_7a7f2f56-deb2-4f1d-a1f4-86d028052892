using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Jobs;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Jobs.GetById;

internal sealed class GetJobByIdQueryHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : IQueryHandler<GetJobByIdQuery, JobResponse>
{
    public async Task<Result<JobResponse>> Handle(GetJobByIdQuery query, CancellationToken cancellationToken)
    {
        Job? job = await context.Jobs
            .AsNoTracking()
            .FirstOrDefaultAsync(j => j.Id == query.JobId && !j.Is<PERSON>eleted, cancellationToken);

        if (job is null)
        {
            return Result.Failure<JobResponse>(JobErrors.NotFound(query.JobId));
        }

        // Authorization check - user can only access their own jobs
        if (userContext.UserId != job.UserId)
        {
            return Result.Failure<JobResponse>(JobErrors.UnauthorizedAccess(query.JobId, userContext.UserId));
        }

        var response = new JobResponse(
            job.Id,
            job.UserId,
            job.JobTitle,
            job.JobDescription,
            job.JobPostingUrl,
            job.CompanyUrl,
            job.AppliedAt,
            job.Status,
            job.BackgroundJobId,
            job.PreferredAIModel,
            job.CreatedAt,
            job.LastModifiedAt);

        return response;
    }
}
