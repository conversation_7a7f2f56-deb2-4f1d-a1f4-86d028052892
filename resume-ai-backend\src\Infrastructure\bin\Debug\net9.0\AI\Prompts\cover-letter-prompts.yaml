# AI Cover Letter Generation Prompts Configuration
# This file contains all prompts and instructions used for AI-powered cover letter generation

prompts:
  cover_letter:
    system_message: |
      You are an expert career counselor and professional writer with over 15 years of 
      experience in creating compelling cover letters. Your task is to generate tailored 
      cover letters that effectively match candidates to job opportunities while 
      maintaining authenticity and professionalism. You understand various industries, 
      job roles, and what hiring managers look for in cover letters.

    user_prompt_template: |
      Please generate a tailored cover letter for the following job application.

      **Job Details:**
      - Position: {job_title}
      - Company: {company_url}
      - Job Description: {job_description}

      **Candidate Information:**
      - Resume Content: {resume_content}

      **Company Research (if available):**
      {company_research}

      **Instructions:**
      {instructions}

      **Response Format:**
      {response_format}

    instructions: |
      1. Create a professional, engaging cover letter tailored to the specific job and company
      2. Use information from the resume to highlight relevant experience and skills
      3. Incorporate company research to show genuine interest and knowledge
      4. Match the tone and style to the company culture and industry
      5. Structure the letter with a strong opening, compelling body, and confident closing
      6. Highlight specific achievements and quantifiable results where possible
      7. Address the job requirements directly with relevant examples
      8. Keep the letter concise, typically 3-4 paragraphs
      9. Maintain authenticity - only reference actual experience from the resume
      10. Use professional language while showing personality and enthusiasm

    response_format: |
      IMPORTANT: Respond with ONLY a valid JSON object. Do NOT wrap your response in markdown code blocks or any other formatting.

      Return a JSON object with these exact properties:
      - "coverLetterContent": The complete cover letter content in professional format
      - "summary": A brief summary of the cover letter approach and key highlights (max 200 characters)
      - "keyHighlights": An array of key points emphasized in the cover letter
      - "confidence": A confidence score (0.0-1.0) for the cover letter quality

      Your response must start with { and end with } - no markdown, no code blocks, no additional text.

      Example format:
      {
        "coverLetterContent": "Dear Hiring Manager,\n\nI am writing to express my strong interest in the Software Engineer position at TechCorp...",
        "summary": "Emphasized technical skills and project experience, highlighted company mission alignment",
        "keyHighlights": [
          "5+ years of software development experience",
          "Led team of 3 developers on major project",
          "Passion for innovative technology solutions"
        ],
        "confidence": 0.88
      }

    fallback_instructions: |
      If you cannot generate a proper cover letter:
      1. Create a generic but professional template
      2. Set confidence to 0.4 or lower
      3. Provide explanation in the summary
      4. Include basic structure with placeholders

# Configuration for different job types
job_types:
  technical:
    additional_instructions: |
      - Emphasize technical skills, programming languages, and frameworks
      - Highlight relevant projects and their technical impact
      - Show problem-solving abilities and innovation
      - Include metrics like performance improvements, user growth, etc.
      - Demonstrate understanding of technical challenges

  creative:
    additional_instructions: |
      - Showcase creative projects and portfolio work
      - Emphasize design thinking and creative process
      - Highlight collaboration with cross-functional teams
      - Show understanding of brand and user experience
      - Include metrics like engagement, conversion rates, etc.

  management:
    additional_instructions: |
      - Emphasize leadership experience and team management
      - Highlight strategic thinking and business impact
      - Show experience with budgets, timelines, and deliverables
      - Demonstrate communication and stakeholder management skills
      - Include metrics like team size, budget managed, revenue impact

  sales:
    additional_instructions: |
      - Emphasize sales achievements and quota performance
      - Highlight relationship building and client management
      - Show understanding of sales processes and methodologies
      - Demonstrate negotiation and closing skills
      - Include specific numbers: deals closed, revenue generated, etc.

# Quality control parameters
quality_control:
  min_confidence_threshold: 0.5
  max_content_length: 5000  # Maximum characters in cover letter
  min_content_length: 500   # Minimum characters for a complete cover letter
  required_elements:
    - "professional greeting"
    - "opening statement"
    - "body paragraphs"
    - "closing statement"
  
  validation_rules:
    - "Must maintain professional tone throughout"
    - "Cannot include fictional experience or skills"
    - "Should be tailored to specific job and company"
    - "Must include specific examples from resume"

# Error handling messages
error_messages:
  insufficient_resume_data: "Resume content is too limited to create a compelling cover letter"
  invalid_job_description: "Job description is unclear or too brief for effective tailoring"
  processing_timeout: "Cover letter generation took too long, returning basic template"
  api_error: "Cover letter service temporarily unavailable, please try again later"
