using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Orchestrates workflows between multiple AI agents
/// </summary>
public interface IAgentOrchestrator
{
    /// <summary>
    /// Execute a complete job application workflow using multiple agents
    /// </summary>
    /// <param name="request">The orchestration request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The orchestration result</returns>
    Task<Result<AgentOrchestrationResponse>> ExecuteWorkflowAsync(
        AgentOrchestrationRequest request, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Execute a specific agent in the context of a workflow
    /// </summary>
    /// <param name="agentType">The type of agent to execute</param>
    /// <param name="context">The shared context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The agent result</returns>
    Task<Result<object>> ExecuteAgentAsync(
        AgentType agentType, 
        AgentContext context, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Request for orchestrating multiple agents
/// </summary>
public sealed record AgentOrchestrationRequest(
    Guid JobApplicationId,
    AgentType[] RequestedAgents,
    bool ExecuteInParallel = false,
    AIModelType? PreferredAIModel = null);

/// <summary>
/// Response from agent orchestration
/// </summary>
public sealed record AgentOrchestrationResponse(
    Guid JobApplicationId,
    Dictionary<AgentType, object> AgentResults,
    Dictionary<AgentType, double> ConfidenceScores,
    TimeSpan TotalExecutionTime,
    bool IsSuccess,
    string? ErrorMessage = null)
{
    /// <summary>
    /// Get result from a specific agent
    /// </summary>
    public T? GetAgentResult<T>(AgentType agentType) where T : class
    {
        return AgentResults.TryGetValue(agentType, out var result) ? result as T : null;
    }
    
    /// <summary>
    /// Get confidence score for a specific agent
    /// </summary>
    public double GetConfidenceScore(AgentType agentType)
    {
        return ConfidenceScores.TryGetValue(agentType, out var score) ? score : 0.0;
    }
}

/// <summary>
/// Configuration for agent execution workflows
/// </summary>
public sealed record AgentWorkflowConfiguration(
    AgentType[] SequentialAgents,
    AgentType[] ParallelAgents,
    Dictionary<AgentType, AgentType[]> Dependencies,
    int MaxRetryAttempts = 3,
    TimeSpan AgentTimeout = default)
{
    public AgentWorkflowConfiguration() : this(
        Array.Empty<AgentType>(),
        Array.Empty<AgentType>(),
        new Dictionary<AgentType, AgentType[]>())
    {
    }
}
