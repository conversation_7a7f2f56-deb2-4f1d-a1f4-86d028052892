using Application.Abstractions.AI;
using Domain.AI;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using SharedKernel;
using System.Text.Json;

namespace Infrastructure.AI;

/// <summary>
/// Agent responsible for generating follow-up email templates
/// </summary>
internal sealed class FollowUpEmailAgent : IFollowUpEmailAgent
{
    private readonly IAIChatServiceFactory _chatServiceFactory;
    private readonly IFollowUpEmailPromptService _promptService;
    private readonly ILogger<FollowUpEmailAgent> _logger;

    public AgentType AgentType => AgentType.FollowUpEmail;

    public FollowUpEmailAgent(
        IAIChatServiceFactory chatServiceFactory,
        IFollowUpEmailPromptService promptService,
        ILogger<FollowUpEmailAgent> logger)
    {
        _chatServiceFactory = chatServiceFactory;
        _promptService = promptService;
        _logger = logger;
    }

    public async Task<Result<FollowUpEmailResponse>> ProcessAsync(
        FollowUpEmailRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting follow-up email generation for JobApplication {JobApplicationId}, Type: {EmailType}",
            request.JobApplicationId, request.EmailType);

        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.JobTitle))
            {
                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.InvalidJobTitle", "Job title cannot be empty"));
            }

            if (string.IsNullOrWhiteSpace(request.CompanyUrl))
            {
                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.InvalidCompanyUrl", "Company URL cannot be empty"));
            }

            // Get chat completion service
            var chatServiceResult = _chatServiceFactory.CreateChatCompletionService(request.PreferredAIModel);
            if (chatServiceResult.IsFailure)
            {
                return Result.Failure<FollowUpEmailResponse>(chatServiceResult.Error);
            }

            var chatCompletionService = chatServiceResult.Value;

            // Build prompts with company research context if available
            var systemMessage = _promptService.GetSystemMessage();
            var userPrompt = BuildUserPrompt(request);

            _logger.LogDebug("Sending follow-up email generation request to AI service");

            // Create chat history
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(systemMessage);
            chatHistory.AddUserMessage(userPrompt);

            // Create execution settings
            var executionSettings = new GeminiPromptExecutionSettings
            {
                ResponseMimeType = "application/json"
            };

            // Call AI service with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(2));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var response = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                cancellationToken: combinedCts.Token);

            if (response?.Content is null || string.IsNullOrEmpty(response.Content))
            {
                _logger.LogError("AI service returned empty response for follow-up email generation");
                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.EmptyResponse", "AI service returned empty response"));
            }

            // Parse AI response
            var emailResponse = ParseAIResponse(response.Content, request.EmailType);

            // Validate response quality
            var qualityControl = _promptService.GetConfiguration().QualityControl;
            if (emailResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                _logger.LogWarning("Follow-up email confidence {Confidence} is below threshold {Threshold}",
                    emailResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            if (emailResponse.EmailContent.Length > qualityControl.MaxContentLength)
            {
                _logger.LogWarning("Follow-up email content length {Length} exceeds maximum {MaxLength}",
                    emailResponse.EmailContent.Length, qualityControl.MaxContentLength);

                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.ContentTooLong", "Follow-up email content exceeds maximum length"));
            }

            if (emailResponse.EmailContent.Length < qualityControl.MinContentLength)
            {
                _logger.LogWarning("Follow-up email content length {Length} is below minimum {MinLength}",
                    emailResponse.EmailContent.Length, qualityControl.MinContentLength);

                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.ContentTooShort", "Follow-up email content is too short"));
            }

            if (emailResponse.EmailSubject.Length > qualityControl.MaxSubjectLength)
            {
                _logger.LogWarning("Follow-up email subject length {Length} exceeds maximum {MaxLength}",
                    emailResponse.EmailSubject.Length, qualityControl.MaxSubjectLength);

                return Result.Failure<FollowUpEmailResponse>(
                    Error.Problem("FollowUpEmailAgent.SubjectTooLong", "Follow-up email subject exceeds maximum length"));
            }

            _logger.LogInformation("Follow-up email generation completed successfully with confidence {Confidence}",
                emailResponse.ConfidenceScore);

            return Result.Success(emailResponse);
        }
        catch (OperationCanceledException ex)
        {
            _logger.LogWarning(ex, "Follow-up email generation was cancelled or timed out");
            return Result.Failure<FollowUpEmailResponse>(
                Error.Problem("FollowUpEmailAgent.Timeout", "Follow-up email generation timed out"));
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response as JSON for follow-up email generation");
            return Result.Failure<FollowUpEmailResponse>(
                Error.Problem("FollowUpEmailAgent.InvalidResponse", "Failed to parse AI response"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during follow-up email generation");
            return Result.Failure<FollowUpEmailResponse>(
                Error.Problem("FollowUpEmailAgent.UnexpectedError", ex.Message));
        }
    }

    private string BuildUserPrompt(FollowUpEmailRequest request)
    {
        var basePrompt = _promptService.GetUserPrompt(
            request.JobTitle,
            request.CompanyUrl,
            request.ApplicationDate,
            request.EmailType);

        // Enhance prompt with company research if available
        if (request.CompanyResearch is not null)
        {
            var companyContext = $"""
                
                **Company Research Results:**
                - Company: {request.CompanyResearch.CompanyName}
                - Industry: {request.CompanyResearch.Industry}
                - Company Size: {request.CompanyResearch.CompanySize}
                - Mission: {request.CompanyResearch.CompanyMission}
                - Values: {request.CompanyResearch.CompanyValues}
                - Recent News: {request.CompanyResearch.RecentNews}
                
                Use this company information to personalize the follow-up email appropriately.
                """;

            basePrompt += companyContext;
        }

        return basePrompt;
    }

    private static FollowUpEmailResponse ParseAIResponse(string aiResponse, FollowUpEmailType emailType)
    {
        try
        {
            // Clean the response to handle markdown code blocks
            string cleanedResponse = CleanMarkdownCodeBlocks(aiResponse);

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonResponse = JsonSerializer.Deserialize<FollowUpEmailJson>(cleanedResponse, jsonOptions);

            return new FollowUpEmailResponse(
                jsonResponse?.EmailSubject ?? "Follow-up on Job Application",
                jsonResponse?.EmailContent ?? "Unable to generate follow-up email content",
                jsonResponse?.Summary ?? "Follow-up email generation completed",
                emailType,
                jsonResponse?.Confidence ?? 0.3,
                DateTime.UtcNow,
                AIModelType.Gemini); // TODO: Get actual model type from context

        }
        catch
        {
            // Fallback if JSON parsing fails
            return new FollowUpEmailResponse(
                "Follow-up on Job Application",
                "Unable to generate follow-up email content due to parsing error",
                "Follow-up email generation failed (fallback parsing)",
                emailType,
                0.2,
                DateTime.UtcNow,
                AIModelType.Gemini);
        }
    }

    private static string CleanMarkdownCodeBlocks(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return content;

        // Remove markdown code block markers
        content = content.Trim();
        if (content.StartsWith("```json"))
        {
            content = content[7..]; // Remove ```json
        }
        else if (content.StartsWith("```"))
        {
            content = content[3..]; // Remove ```
        }

        if (content.EndsWith("```"))
        {
            content = content[..^3]; // Remove trailing ```
        }

        return content.Trim();
    }

    private sealed class FollowUpEmailJson
    {
        public string? EmailSubject { get; set; }
        public string? EmailContent { get; set; }
        public string? Summary { get; set; }
        public string? EmailType { get; set; }
        public double? Confidence { get; set; }
    }
}
