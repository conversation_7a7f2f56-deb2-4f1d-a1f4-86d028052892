﻿using Domain.Jobs;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Jobs;

internal sealed class JobConfiguration : IEntityTypeConfiguration<Job>
{
    public void Configure(EntityTypeBuilder<Job> builder)
    {
        builder.HasKey(t => t.Id);

        builder.Property(t => t.AppliedAt).HasConversion(d => d != null ? DateTime.SpecifyKind(d.Value, DateTimeKind.Utc) : d, v => v);

        builder.Property(t => t.BackgroundJobId)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.HasOne<User>().WithMany().HasForeignKey(t => t.UserId);
    }
}
