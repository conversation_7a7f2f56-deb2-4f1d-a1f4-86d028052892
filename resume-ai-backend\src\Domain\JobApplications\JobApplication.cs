using Domain.AI;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using SharedKernel;

namespace Domain.JobApplications;

public class JobApplication : BaseEntity
{
    public Guid ResumeId { get; set; }
    public Guid JobId { get; set; }
    public Guid CreatedBy { get; set; }
    public JobApplicationStatus Status { get; set; }

    // AI Customization Results
    public string? AICustomizedContent { get; set; }
    public string? AICustomizationSummary { get; set; }
    public string? AICustomizedKeyChanges { get; set; }
    public double? AIConfidenceScore { get; set; }
    public DateTime? AIProcessedAt { get; set; }

    // Multi-Agent AI Results
    public string? CompanyResearchData { get; set; }
    public double? CompanyResearchConfidence { get; set; }
    public DateTime? CompanyResearchProcessedAt { get; set; }

    public string? CoverLetterContent { get; set; }
    public string? CoverLetterSummary { get; set; }
    public string? CoverLetterKeyHighlights { get; set; }
    public double? CoverLetterConfidence { get; set; }
    public DateTime? CoverLetterProcessedAt { get; set; }

    public string? FollowUpEmailSubject { get; set; }
    public string? FollowUpEmailContent { get; set; }
    public string? FollowUpEmailSummary { get; set; }
    public string? FollowUpEmailType { get; set; }
    public double? FollowUpEmailConfidence { get; set; }
    public DateTime? FollowUpEmailProcessedAt { get; set; }

    // Navigation properties
    public Resume Resume { get; set; }
    public Job Job { get; set; }
    public User CreatedByUser { get; set; }

    public static JobApplication Create(Guid resumeId, Guid jobId, Guid createdBy)
    {
        var jobApplication = new JobApplication
        {
            ResumeId = resumeId,
            JobId = jobId,
            CreatedBy = createdBy,
            Status = JobApplicationStatus.Applied
        };

        jobApplication.Raise(new JobApplicationCreatedDomainEvent(jobApplication.Id));

        return jobApplication;
    }

    public void UpdateAICustomization(string customizedContent, string summary, string keyChanges, double confidenceScore)
    {
        AICustomizedContent = customizedContent;
        AICustomizationSummary = summary;
        AICustomizedKeyChanges = keyChanges;
        AIConfidenceScore = confidenceScore;
        AIProcessedAt = DateTime.UtcNow;

        Raise(new JobApplicationAICustomizationCompletedDomainEvent(Id, confidenceScore));
    }

    public void UpdateCompanyResearch(string companyData, double confidenceScore)
    {
        CompanyResearchData = companyData;
        CompanyResearchConfidence = confidenceScore;
        CompanyResearchProcessedAt = DateTime.UtcNow;

        Raise(new CompanyResearchCompletedDomainEvent(Id, ExtractCompanyName(companyData), "", confidenceScore));
    }

    public void UpdateCoverLetter(string content, string summary, string keyHighlights, double confidenceScore)
    {
        CoverLetterContent = content;
        CoverLetterSummary = summary;
        CoverLetterKeyHighlights = keyHighlights;
        CoverLetterConfidence = confidenceScore;
        CoverLetterProcessedAt = DateTime.UtcNow;

        Raise(new CoverLetterGeneratedDomainEvent(Id, Job?.JobTitle ?? "", confidenceScore));
    }

    public void UpdateFollowUpEmail(string subject, string content, string summary, string emailType, double confidenceScore)
    {
        FollowUpEmailSubject = subject;
        FollowUpEmailContent = content;
        FollowUpEmailSummary = summary;
        FollowUpEmailType = emailType;
        FollowUpEmailConfidence = confidenceScore;
        FollowUpEmailProcessedAt = DateTime.UtcNow;

        Raise(new FollowUpEmailGeneratedDomainEvent(Id, emailType, confidenceScore));
    }

    private static string ExtractCompanyName(string companyData)
    {
        // Simple extraction - in real implementation, this would parse JSON
        return "Company"; // Placeholder
    }

    public void UpdateStatus(JobApplicationStatus newStatus)
    {
        if (Status != newStatus)
        {
            JobApplicationStatus oldStatus = Status;
            Status = newStatus;
            
            Raise(new JobApplicationStatusChangedDomainEvent(Id, oldStatus, newStatus));
        }
    }
}
