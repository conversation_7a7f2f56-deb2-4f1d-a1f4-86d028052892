using Application.Abstractions.AI;
using Application.Abstractions.Messaging;
using Domain.AI;

namespace Application.AI.GenerateCoverLetter;

public sealed record GenerateCoverLetterCommand(
    Guid JobApplicationId,
    string JobTitle,
    string JobDescription,
    string CompanyUrl,
    string ResumeContent,
    CompanyResearchResponse? CompanyResearch = null,
    AIModelType? PreferredAIModel = null) : ICommand<CoverLetterResponse>;
