using Domain.AI;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.EventHandlers;

internal sealed class AgentWorkflowStartedDomainEventHandler : IDomainEventHandler<AgentWorkflowStartedDomainEvent>
{
    private readonly ILogger<AgentWorkflowStartedDomainEventHandler> _logger;

    public AgentWorkflowStartedDomainEventHandler(ILogger<AgentWorkflowStartedDomainEventHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(AgentWorkflowStartedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Multi-agent workflow started for JobApplication {JobApplicationId} with agents: {Agents}, AI Model: {AIModel}",
            domainEvent.JobApplicationId,
            string.Join(", ", domainEvent.RequestedAgents),
            domainEvent.PreferredAIModel?.ToString() ?? "Default");

        // TODO: Add any additional logic needed when agent workflow starts
        // For example: send notifications, update analytics, etc.

        return Task.CompletedTask;
    }
}
