using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.JobApplications;

internal sealed class JobApplicationConfiguration : IEntityTypeConfiguration<JobApplication>
{
    public void Configure(EntityTypeBuilder<JobApplication> builder)
    {
        builder.HasKey(ja => ja.Id);

        builder.Property(ja => ja.Status)
            .HasConversion<int>();

        // AI Customization properties
        builder.Property(ja => ja.AICustomizedContent)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ja => ja.AICustomizationSummary)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(ja => ja.AIConfidenceScore)
            .HasPrecision(3, 2)
            .IsRequired(false);

        builder.Property(ja => ja.AIProcessedAt)
            .IsRequired(false);

        // Multi-Agent AI properties
        builder.Property(ja => ja.CompanyResearchData)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ja => ja.CompanyResearchConfidence)
            .HasPrecision(3, 2)
            .IsRequired(false);

        builder.Property(ja => ja.CompanyResearchProcessedAt)
            .IsRequired(false);

        builder.Property(ja => ja.CoverLetterContent)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ja => ja.CoverLetterSummary)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(ja => ja.CoverLetterKeyHighlights)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ja => ja.CoverLetterConfidence)
            .HasPrecision(3, 2)
            .IsRequired(false);

        builder.Property(ja => ja.CoverLetterProcessedAt)
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailSubject)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailContent)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailSummary)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailType)
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailConfidence)
            .HasPrecision(3, 2)
            .IsRequired(false);

        builder.Property(ja => ja.FollowUpEmailProcessedAt)
            .IsRequired(false);

        // Foreign key relationships with navigation properties properly linked
        builder.HasOne(ja => ja.Resume)
            .WithMany()
            .HasForeignKey(ja => ja.ResumeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ja => ja.Job)
            .WithMany()
            .HasForeignKey(ja => ja.JobId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ja => ja.CreatedByUser)
            .WithMany()
            .HasForeignKey(ja => ja.CreatedBy)
            .OnDelete(DeleteBehavior.Restrict);

        // Unique constraint to prevent duplicate applications for the same resume-job combination
        builder.HasIndex(ja => new { ja.ResumeId, ja.JobId })
            .IsUnique();
    }
}
