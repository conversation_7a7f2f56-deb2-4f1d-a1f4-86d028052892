using Application.Abstractions.AI;
using Application.Abstractions.Messaging;
using Domain.AI;

namespace Application.AI.GenerateFollowUpEmail;

public sealed record GenerateFollowUpEmailCommand(
    Guid JobApplicationId,
    string JobTitle,
    string CompanyUrl,
    DateTime ApplicationDate,
    FollowUpEmailType EmailType = FollowUpEmailType.Initial,
    CompanyResearchResponse? CompanyResearch = null,
    AIModelType? PreferredAIModel = null) : ICommand<FollowUpEmailResponse>;
