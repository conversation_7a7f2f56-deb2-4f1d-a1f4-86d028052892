using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Domain.AI;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;
using System.Diagnostics;

namespace Infrastructure.AI;

/// <summary>
/// Orchestrates workflows between multiple AI agents
/// </summary>
internal sealed class AgentOrchestrator : IAgentOrchestrator
{
    private readonly IAgentFactory _agentFactory;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<AgentOrchestrator> _logger;

    public AgentOrchestrator(
        IAgentFactory agentFactory,
        IApplicationDbContext dbContext,
        ILogger<AgentOrchestrator> logger)
    {
        _agentFactory = agentFactory;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result<AgentOrchestrationResponse>> ExecuteWorkflowAsync(
        AgentOrchestrationRequest request,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Starting agent workflow for JobApplication {JobApplicationId} with agents: {Agents}",
            request.JobApplicationId, string.Join(", ", request.RequestedAgents));

        try
        {
            // Get job application context
            var contextResult = await GetAgentContextAsync(request.JobApplicationId, request.PreferredAIModel, cancellationToken);
            if (contextResult.IsFailure)
            {
                return Result.Failure<AgentOrchestrationResponse>(contextResult.Error);
            }

            var context = contextResult.Value;
            
            // Raise workflow started event
            var jobApplication = await _dbContext.JobApplications
                .FirstOrDefaultAsync(ja => ja.Id == request.JobApplicationId, cancellationToken);
            
            if (jobApplication is not null)
            {
                jobApplication.Raise(new AgentWorkflowStartedDomainEvent(
                    request.JobApplicationId, 
                    request.RequestedAgents, 
                    request.PreferredAIModel));
            }

            var agentResults = new Dictionary<AgentType, object>();
            var confidenceScores = new Dictionary<AgentType, double>();
            var errors = new List<string>();

            if (request.ExecuteInParallel)
            {
                // Execute agents in parallel
                var tasks = request.RequestedAgents.Select(agentType => 
                    ExecuteAgentWithErrorHandling(agentType, context, cancellationToken));
                
                var results = await Task.WhenAll(tasks);
                
                foreach (var (agentType, result) in results)
                {
                    if (result.IsSuccess)
                    {
                        agentResults[agentType] = result.Value;
                        confidenceScores[agentType] = GetConfidenceScore(result.Value);
                        context = context.WithAgentResult(agentType, result.Value);
                    }
                    else
                    {
                        errors.Add($"{agentType}: {result.Error.Description}");
                        _logger.LogError("Agent {AgentType} failed: {Error}", agentType, result.Error.Description);
                    }
                }
            }
            else
            {
                // Execute agents sequentially
                foreach (var agentType in request.RequestedAgents)
                {
                    var result = await ExecuteAgentAsync(agentType, context, cancellationToken);
                    
                    if (result.IsSuccess)
                    {
                        agentResults[agentType] = result.Value;
                        confidenceScores[agentType] = GetConfidenceScore(result.Value);
                        context = context.WithAgentResult(agentType, result.Value);
                    }
                    else
                    {
                        errors.Add($"{agentType}: {result.Error.Description}");
                        _logger.LogError("Agent {AgentType} failed: {Error}", agentType, result.Error.Description);
                        
                        // For sequential execution, we might want to stop on first failure
                        // or continue based on configuration
                    }
                }
            }

            stopwatch.Stop();
            
            // Raise workflow completed event
            if (jobApplication is not null)
            {
                jobApplication.Raise(new AgentWorkflowCompletedDomainEvent(
                    request.JobApplicationId,
                    agentResults.Keys.ToArray(),
                    confidenceScores,
                    stopwatch.Elapsed,
                    errors.Count == 0,
                    errors.Count > 0 ? string.Join("; ", errors) : null));
                
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            var response = new AgentOrchestrationResponse(
                request.JobApplicationId,
                agentResults,
                confidenceScores,
                stopwatch.Elapsed,
                errors.Count == 0,
                errors.Count > 0 ? string.Join("; ", errors) : null);

            _logger.LogInformation("Completed agent workflow for JobApplication {JobApplicationId} in {Duration}ms. Success: {Success}",
                request.JobApplicationId, stopwatch.ElapsedMilliseconds, response.IsSuccess);

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Unexpected error during agent workflow execution for JobApplication {JobApplicationId}",
                request.JobApplicationId);
            
            return Result.Failure<AgentOrchestrationResponse>(
                Error.Problem("AgentOrchestrator.UnexpectedError", ex.Message));
        }
    }

    public async Task<Result<object>> ExecuteAgentAsync(
        AgentType agentType,
        AgentContext context,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Executing agent {AgentType} for JobApplication {JobApplicationId}",
            agentType, context.JobApplicationId);

        try
        {
            var result = agentType switch
            {
                AgentType.ResumeCustomization => await ExecuteResumeCustomizationAgent(context, cancellationToken),
                AgentType.CompanyResearch => await ExecuteCompanyResearchAgent(context, cancellationToken),
                AgentType.CoverLetter => await ExecuteCoverLetterAgent(context, cancellationToken),
                AgentType.FollowUpEmail => await ExecuteFollowUpEmailAgent(context, cancellationToken),
                _ => Result.Failure<object>(AgentFactoryErrors.AgentNotSupported(agentType))
            };

            if (result.IsSuccess)
            {
                _logger.LogInformation("Agent {AgentType} completed successfully for JobApplication {JobApplicationId}",
                    agentType, context.JobApplicationId);
            }
            else
            {
                _logger.LogError("Agent {AgentType} failed for JobApplication {JobApplicationId}: {Error}",
                    agentType, context.JobApplicationId, result.Error.Description);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error executing agent {AgentType} for JobApplication {JobApplicationId}",
                agentType, context.JobApplicationId);
            
            return Result.Failure<object>(
                Error.Problem("AgentOrchestrator.AgentExecutionError", ex.Message));
        }
    }

    private async Task<(AgentType AgentType, Result<object> Result)> ExecuteAgentWithErrorHandling(
        AgentType agentType,
        AgentContext context,
        CancellationToken cancellationToken)
    {
        var result = await ExecuteAgentAsync(agentType, context, cancellationToken);
        return (agentType, result);
    }

    private async Task<Result<AgentContext>> GetAgentContextAsync(
        Guid jobApplicationId,
        AIModelType? preferredAIModel,
        CancellationToken cancellationToken)
    {
        var jobApplication = await _dbContext.JobApplications
            .Include(ja => ja.Job)
            .Include(ja => ja.Resume)
            .FirstOrDefaultAsync(ja => ja.Id == jobApplicationId, cancellationToken);

        if (jobApplication is null)
        {
            return Result.Failure<AgentContext>(
                Error.NotFound("AgentOrchestrator.JobApplicationNotFound", 
                    $"Job application with ID {jobApplicationId} was not found"));
        }

        var context = new AgentContext(
            jobApplicationId,
            jobApplication.JobId,
            jobApplication.CreatedBy,
            jobApplication.Job.JobTitle,
            jobApplication.Job.JobDescription,
            jobApplication.Job.CompanyUrl,
            jobApplication.Resume.ResumeContent,
            preferredAIModel ?? jobApplication.Job.PreferredAIModel);

        return Result.Success(context);
    }

    private static double GetConfidenceScore(object agentResult)
    {
        return agentResult switch
        {
            AgentResponse response => response.ConfidenceScore,
            _ => 0.0
        };
    }

    private async Task<Result<object>> ExecuteResumeCustomizationAgent(
        AgentContext context,
        CancellationToken cancellationToken)
    {
        var agentResult = _agentFactory.CreateResumeCustomizationAgent();
        if (agentResult.IsFailure)
        {
            return Result.Failure<object>(agentResult.Error);
        }

        var agent = agentResult.Value;
        var companyResearch = context.GetAgentResult<CompanyResearchResponse>(AgentType.CompanyResearch);

        var request = new ResumeCustomizationRequest(
            context.JobApplicationId,
            context.JobTitle,
            context.JobDescription,
            context.CompanyUrl,
            context.OriginalResumeContent,
            companyResearch,
            context.PreferredAIModel);

        var result = await agent.ProcessAsync(request, cancellationToken);
        return result.IsSuccess ? Result.Success<object>(result.Value) : Result.Failure<object>(result.Error);
    }

    private async Task<Result<object>> ExecuteCompanyResearchAgent(
        AgentContext context,
        CancellationToken cancellationToken)
    {
        var agentResult = _agentFactory.CreateCompanyResearchAgent();
        if (agentResult.IsFailure)
        {
            return Result.Failure<object>(agentResult.Error);
        }

        var agent = agentResult.Value;
        var request = new CompanyResearchRequest(
            context.JobApplicationId,
            context.CompanyUrl,
            context.JobTitle,
            context.PreferredAIModel);

        var result = await agent.ProcessAsync(request, cancellationToken);
        return result.IsSuccess ? Result.Success<object>(result.Value) : Result.Failure<object>(result.Error);
    }

    private async Task<Result<object>> ExecuteCoverLetterAgent(
        AgentContext context,
        CancellationToken cancellationToken)
    {
        var agentResult = _agentFactory.CreateCoverLetterAgent();
        if (agentResult.IsFailure)
        {
            return Result.Failure<object>(agentResult.Error);
        }

        var agent = agentResult.Value;
        var companyResearch = context.GetAgentResult<CompanyResearchResponse>(AgentType.CompanyResearch);

        var request = new CoverLetterRequest(
            context.JobApplicationId,
            context.JobTitle,
            context.JobDescription,
            context.CompanyUrl,
            context.OriginalResumeContent,
            companyResearch,
            context.PreferredAIModel);

        var result = await agent.ProcessAsync(request, cancellationToken);
        return result.IsSuccess ? Result.Success<object>(result.Value) : Result.Failure<object>(result.Error);
    }

    private async Task<Result<object>> ExecuteFollowUpEmailAgent(
        AgentContext context,
        CancellationToken cancellationToken)
    {
        var agentResult = _agentFactory.CreateFollowUpEmailAgent();
        if (agentResult.IsFailure)
        {
            return Result.Failure<object>(agentResult.Error);
        }

        var agent = agentResult.Value;
        var companyResearch = context.GetAgentResult<CompanyResearchResponse>(AgentType.CompanyResearch);

        var request = new FollowUpEmailRequest(
            context.JobApplicationId,
            context.JobTitle,
            context.CompanyUrl,
            DateTime.UtcNow, // Application date - should be retrieved from job application
            FollowUpEmailType.Initial,
            companyResearch,
            context.PreferredAIModel);

        var result = await agent.ProcessAsync(request, cancellationToken);
        return result.IsSuccess ? Result.Success<object>(result.Value) : Result.Failure<object>(result.Error);
    }
}
