using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.GenerateCoverLetter;

internal sealed class GenerateCoverLetterCommandHandler 
    : ICommandHandler<GenerateCoverLetterCommand, CoverLetterResponse>
{
    private readonly ICoverLetterAgent _agent;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<GenerateCoverLetterCommandHandler> _logger;

    public GenerateCoverLetterCommandHandler(
        ICoverLetterAgent agent,
        IApplicationDbContext dbContext,
        ILogger<GenerateCoverLetterCommandHandler> logger)
    {
        _agent = agent;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Result<CoverLetterResponse>> Handle(
        GenerateCoverLetterCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating cover letter for JobApplication {JobApplicationId}, Position: {JobTitle}",
            request.JobApplicationId, request.JobTitle);

        try
        {
            // Validate job application exists
            var jobApplication = await _dbContext.JobApplications
                .FirstOrDefaultAsync(ja => ja.Id == request.JobApplicationId, cancellationToken);

            if (jobApplication is null)
            {
                return Result.Failure<CoverLetterResponse>(
                    Error.NotFound("GenerateCoverLetter.JobApplicationNotFound",
                        $"Job application with ID {request.JobApplicationId} was not found"));
            }

            // Create agent request
            var agentRequest = new CoverLetterRequest(
                request.JobApplicationId,
                request.JobTitle,
                request.JobDescription,
                request.CompanyUrl,
                request.ResumeContent,
                request.CompanyResearch,
                request.PreferredAIModel);

            // Execute agent
            var agentResult = await _agent.ProcessAsync(agentRequest, cancellationToken);

            if (agentResult.IsFailure)
            {
                _logger.LogError("Cover letter generation failed for JobApplication {JobApplicationId}: {Error}",
                    request.JobApplicationId, agentResult.Error.Description);
                return Result.Failure<CoverLetterResponse>(agentResult.Error);
            }

            var response = agentResult.Value;

            // Update job application with results
            jobApplication.UpdateCoverLetter(
                response.CoverLetterContent,
                response.Summary,
                string.Join(",", response.KeyHighlights),
                response.ConfidenceScore);

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Cover letter generation completed successfully for JobApplication {JobApplicationId} with confidence {Confidence}",
                request.JobApplicationId, response.ConfidenceScore);

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error generating cover letter for JobApplication {JobApplicationId}",
                request.JobApplicationId);

            return Result.Failure<CoverLetterResponse>(
                Error.Problem("GenerateCoverLetter.UnexpectedError", ex.Message));
        }
    }
}
