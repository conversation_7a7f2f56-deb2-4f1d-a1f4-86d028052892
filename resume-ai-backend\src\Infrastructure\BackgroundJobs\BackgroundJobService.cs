using Application.Abstractions.BackgroundJobs;
using Hangfire;

namespace Infrastructure.BackgroundJobs;

public class BackgroundJobService : IBackgroundJobService
{
    private readonly IBackgroundJobClient _backgroundJobClient;

    public BackgroundJobService(IBackgroundJobClient backgroundJobClient)
    {
        _backgroundJobClient = backgroundJobClient;
    }

    public string EnqueueAIResumeCustomization(Guid jobId)
    {
        return _backgroundJobClient.Enqueue<AIResumeCustomizationJob>(
            job => job.ProcessAICustomizationAsync(jobId, CancellationToken.None));
    }

    public string EnqueueMultiAgentWorkflow(Guid jobId)
    {
        return _backgroundJobClient.Enqueue<MultiAgentWorkflowJob>(
            job => job.ProcessMultiAgentWorkflowAsync(jobId, CancellationToken.None));
    }
}
