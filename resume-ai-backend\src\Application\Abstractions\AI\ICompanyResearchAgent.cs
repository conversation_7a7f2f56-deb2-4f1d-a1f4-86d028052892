using Domain.AI;
using SharedKernel;

namespace Application.Abstractions.AI;

/// <summary>
/// Agent responsible for researching company information from URLs
/// </summary>
public interface ICompanyResearchAgent : IAgent<CompanyResearchRequest, CompanyResearchResponse>
{
}

/// <summary>
/// Request for company research
/// </summary>
public sealed record CompanyResearchRequest(
    Guid JobApplicationId,
    string CompanyUrl,
    string JobTitle,
    AIModelType? PreferredAIModel = null) : AgentRequest(JobApplicationId, PreferredAIModel);

/// <summary>
/// Response from company research
/// </summary>
public sealed record CompanyResearchResponse(
    string CompanyName,
    string CompanyDescription,
    string Industry,
    string CompanySize,
    string CompanyValues,
    string RecentNews,
    string KeyProducts,
    string CompanyMission,
    double ConfidenceScore,
    DateTime ProcessedAt,
    AIModelType AIModelUsed) : AgentResponse(ConfidenceScore, ProcessedAt, AIModelUsed);
