using Application.Abstractions.Messaging;
using Application.Jobs.Create;
using Application.Jobs.GetById;
using Domain.AI;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class Create : IEndpoint
{
    public sealed record CreateJobRequest(
        string JobTitle,
        string JobDescription,
        string JobPostingUrl,
        string CompanyUrl,
        AIModelType? PreferredAIModel = null);

    public sealed record CreateJobResponse(
        Guid JobId,
        string? BackgroundJobId,
        string Message);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("jobs", async (
            CreateJobRequest request,
            ICommandHandler<CreateJobCommand, Guid> createHandler,
            IQueryHandler<GetJobByIdQuery, JobResponse> queryHandler,
            CancellationToken cancellationToken) =>
        {
            var command = new CreateJobCommand(
                request.JobTitle,
                request.JobDescription,
                request.JobPostingUrl,
                request.CompanyUrl,
                request.PreferredAIModel);

            Result<Guid> createResult = await createHandler.Handle(command, cancellationToken);

            if (createResult.IsFailure)
            {
                return createResult.Match(
                    _ => Results.Ok(),
                    CustomResults.Problem);
            }

            // Wait a moment for the domain event to be processed and background job to be enqueued
            await Task.Delay(100, cancellationToken);

            // Get the job with background job ID
            var getJobQuery = new GetJobByIdQuery(createResult.Value);
            var jobResult = await queryHandler.Handle(getJobQuery, cancellationToken);

            if (jobResult.IsFailure)
            {
                // Fallback if we can't get the job details
                var fallbackResponse = new CreateJobResponse(
                    createResult.Value,
                    null,
                    "Job created successfully. Multi-agent AI workflow is being processed in the background.");

                return Results.Accepted($"/jobs/{createResult.Value}", fallbackResponse);
            }

            var response = new CreateJobResponse(
                jobResult.Value.Id,
                jobResult.Value.BackgroundJobId,
                "Job created successfully. Multi-agent AI workflow is being processed in the background.");

            return Results.Accepted($"/jobs/{createResult.Value}", response);
        })
        .WithTags(Tags.Jobs)
        .WithName("CreateJob")
        .Produces<CreateJobResponse>(StatusCodes.Status202Accepted)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
