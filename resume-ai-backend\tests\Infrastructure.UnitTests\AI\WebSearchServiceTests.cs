using FluentAssertions;
using Infrastructure.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;

namespace Infrastructure.UnitTests.AI;

public class WebSearchServiceTests
{
    private readonly ILogger<WebSearchService> _logger;
    private readonly IOptions<WebSearchOptions> _options;

    public WebSearchServiceTests()
    {
        _logger = Substitute.For<ILogger<WebSearchService>>();
        _options = Substitute.For<IOptions<WebSearchOptions>>();
        
        _options.Value.Returns(new WebSearchOptions
        {
            GoogleSearchEngineId = "test-search-engine-id",
            GoogleApiKey = "test-api-key",
            MaxResults = 10,
            TimeoutSeconds = 30
        });
    }

    [Fact]
    public void WebSearchService_Constructor_ShouldInitializeCorrectly()
    {
        // Act
        using var service = new WebSearchService(_options, _logger);

        // Assert
        service.Should().NotBeNull();
    }

    [Theory]
    [InlineData("https://microsoft.com", "Software Engineer")]
    [InlineData("https://google.com", "Product Manager")]
    [InlineData("https://amazon.com", "Data Scientist")]
    public async Task SearchCompanyInformationAsync_WithValidInputs_ShouldReturnResult(
        string companyUrl, 
        string jobTitle)
    {
        // Arrange
        using var service = new WebSearchService(_options, _logger);

        // Act
        var result = await service.SearchCompanyInformationAsync(
            companyUrl, 
            jobTitle, 
            CancellationToken.None);

        // Assert
        // Note: This test will fail without valid API credentials
        // In a real test environment, you would either:
        // 1. Use test credentials
        // 2. Mock the GoogleTextSearch dependency
        // 3. Use integration test category with real credentials
        result.Should().NotBeNull();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("invalid-url")]
    public async Task SearchCompanyInformationAsync_WithInvalidUrl_ShouldHandleGracefully(
        string invalidUrl)
    {
        // Arrange
        using var service = new WebSearchService(_options, _logger);

        // Act
        var result = await service.SearchCompanyInformationAsync(
            invalidUrl, 
            "Software Engineer", 
            CancellationToken.None);

        // Assert
        // The service should handle invalid URLs gracefully
        result.Should().NotBeNull();
    }

    [Fact]
    public void WebSearchOptions_DefaultValues_ShouldBeCorrect()
    {
        // Arrange & Act
        var options = new WebSearchOptions();

        // Assert
        options.GoogleSearchEngineId.Should().Be(string.Empty);
        options.GoogleApiKey.Should().Be(string.Empty);
        options.MaxResults.Should().Be(10);
        options.TimeoutSeconds.Should().Be(30);
    }

    [Fact]
    public void CompanySearchResults_GetCombinedContent_WithNoResults_ShouldReturnDefaultMessage()
    {
        // Arrange
        var results = new CompanySearchResults(
            "Test Company",
            new List<SearchResult>().AsReadOnly(),
            0.1);

        // Act
        var content = results.GetCombinedContent();

        // Assert
        content.Should().Contain("No search results available");
    }

    [Fact]
    public void CompanySearchResults_GetCombinedContent_WithResults_ShouldFormatCorrectly()
    {
        // Arrange
        var searchResults = new List<SearchResult>
        {
            new("Test Source 1", "Test content 1", "https://test1.com"),
            new("Test Source 2", "Test content 2", "https://test2.com")
        };

        var results = new CompanySearchResults(
            "Test Company",
            searchResults.AsReadOnly(),
            0.8);

        // Act
        var content = results.GetCombinedContent();

        // Assert
        content.Should().Contain("Company: Test Company");
        content.Should().Contain("Source: Test Source 1");
        content.Should().Contain("URL: https://test1.com");
        content.Should().Contain("Content: Test content 1");
        content.Should().Contain("Source: Test Source 2");
        content.Should().Contain("URL: https://test2.com");
        content.Should().Contain("Content: Test content 2");
        content.Should().Contain("---");
    }

    [Fact]
    public void CompanySearchResults_GetCombinedContent_WithManyResults_ShouldLimitToTopFive()
    {
        // Arrange
        var searchResults = new List<SearchResult>();
        for (int i = 1; i <= 10; i++)
        {
            searchResults.Add(new SearchResult($"Source {i}", $"Content {i}", $"https://test{i}.com"));
        }

        var results = new CompanySearchResults(
            "Test Company",
            searchResults.AsReadOnly(),
            0.9);

        // Act
        var content = results.GetCombinedContent();

        // Assert
        content.Should().Contain("Source 1");
        content.Should().Contain("Source 5");
        content.Should().NotContain("Source 6"); // Should be limited to top 5
    }

    [Theory]
    [InlineData("https://microsoft.com", "microsoft")]
    [InlineData("https://www.google.com", "google")]
    [InlineData("https://amazon.co.uk", "amazon")]
    [InlineData("https://sub.example.com", "sub")]
    public void ExtractCompanyNameFromUrl_ShouldExtractCorrectly(string url, string expectedName)
    {
        // This tests the private method indirectly through the public API
        // In a real implementation, you might make this method internal for testing
        
        // Arrange
        using var service = new WebSearchService(_options, _logger);

        // Act & Assert
        // We can't directly test the private method, but we can verify
        // that the service handles different URL formats correctly
        var task = service.SearchCompanyInformationAsync(url, "Test Job", CancellationToken.None);
        
        // The method should not throw an exception for valid URLs
        task.Should().NotBeNull();
    }
}
