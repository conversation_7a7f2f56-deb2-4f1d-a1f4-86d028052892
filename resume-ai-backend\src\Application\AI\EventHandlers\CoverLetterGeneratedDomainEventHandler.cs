using Domain.AI;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.AI.EventHandlers;

internal sealed class CoverLetterGeneratedDomainEventHandler : IDomainEventHandler<CoverLetterGeneratedDomainEvent>
{
    private readonly ILogger<CoverLetterGeneratedDomainEventHandler> _logger;

    public CoverLetterGeneratedDomainEventHandler(ILogger<CoverLetterGeneratedDomainEventHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(CoverLetterGeneratedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Cover letter generated for JobApplication {JobApplicationId}. " +
            "Job Title: {JobTitle}, Confidence: {Confidence}",
            domainEvent.JobApplicationId,
            domainEvent.JobTitle,
            domainEvent.ConfidenceScore);

        // TODO: Add any additional logic needed when cover letter is generated
        // For example: send notifications, update analytics, trigger document generation, etc.

        return Task.CompletedTask;
    }
}
